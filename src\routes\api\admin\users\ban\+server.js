// src/routes/api/admin/users/ban/+server.js
import { json } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import { ADMIN_USERS } from '$lib/constants/adminUsers';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Use the centralized admin users list
const PERMITTED_USER_IDS = ADMIN_USERS;

export async function POST({ request, locals }) {
  const { session, user } = await locals.safeGetSession();
  if (!session || !user || !PERMITTED_USER_IDS.includes(user.id)) {
    throw error(401, 'Unauthorized');
  }

  const { userId, reason } = await request.json();

  // First verify the user exists
  const { data: userExists, error: userCheckError } = await supabase
    .from('profiles')
    .select('id')
    .eq('id', userId)
    .single();

  if (userCheckError || !userExists) {
    throw error(404, 'User not found');
  }

  try {
    const { error: banError } = await supabase.rpc('ban_user', {
      user_id_param: userExists.id,
      admin_id_param: user.id,
      reason_param: reason || 'No reason provided',
      timestamp_param: new Date().toISOString()
    });

    if (banError) {
      console.error('Ban error:', banError);
      throw error(500, {
        message: 'Failed to ban user',
        details: banError
      });
    }

    return json({ success: true });
  } catch (err) {
    console.error('Error in ban endpoint:', err);
    throw error(500, {
      message: err.message || 'Failed to ban user',
      details: err
    });
  }
}