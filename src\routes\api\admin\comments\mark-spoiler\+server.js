// src/routes/api/admin/comments/mark-spoiler/+server.js
import { json } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import { ADMIN_USERS } from '$lib/constants/adminUsers';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Use the centralized admin users list
const PERMITTED_USER_IDS = ADMIN_USERS;

export async function POST({ request, locals }) {
  // Verify user is authenticated and authorized
  const { session, user } = await locals.safeGetSession();
  if (!session || !user || !PERMITTED_USER_IDS.includes(user.id)) {
    throw error(401, 'Unauthorized');
  }

  try {
    const { commentId } = await request.json();

    if (!commentId) {
      throw error(400, 'Comment ID is required');
    }

    // Update the comment in the database
    const { data, error: updateError } = await supabase
      .from('comments')
      .update({ is_spoiler: true })
      .eq('id', commentId)
      .select()
      .single();

    if (updateError) {
      console.error('Error marking comment as spoiler:', updateError);
      throw error(500, 'Failed to mark comment as spoiler');
    }

    return json({
      success: true,
      message: 'Comment marked as spoiler successfully',
      comment: data
    });
  } catch (err) {
    console.error('Error in mark-spoiler endpoint:', err);
    throw error(500, err.message || 'An error occurred while marking comment as spoiler');
  }
}