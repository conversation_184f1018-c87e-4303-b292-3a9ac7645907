// src/routes/api/admin/episodes/search/+server.js
import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import { ADMIN_USERS } from '$lib/constants/adminUsers';

// Initialize Supabase client with service role key for admin operations
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function GET({ url, locals }) {
  // Check if user is authenticated and is an admin
  const { session, user } = await locals.safeGetSession();

  if (!session || !user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Check if user is an admin by ID
  const isAdmin = ADMIN_USERS.includes(user.id);

  if (!isAdmin) {
    return json({ error: 'Forbidden' }, { status: 403 });
  }

  try {
    const query = url.searchParams.get('query') || '';
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const page = parseInt(url.searchParams.get('page') || '0');
    const offset = page * limit;

    let supabaseQuery = supabase
      .from('anime')
      .select('id, title, episode_number, anilist_id, created_at', { count: 'exact' });

    // Apply search filter if query is provided
    if (query) {
      supabaseQuery = supabaseQuery.ilike('title', `%${query}%`);
    }

    // Apply pagination
    const { data, error, count } = await supabaseQuery
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error searching episodes:', error);
      return json({ error: 'Failed to search episodes' }, { status: 500 });
    }

    return json({
      episodes: data,
      totalCount: count,
      page,
      limit
    });
  } catch (error) {
    console.error('Error processing request:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
}
