<script>
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { Hourglass, Calendar, Tv, Film, Clapperboard } from 'lucide-svelte';
	import { generateAnimeUrl, getCachedColor, setCachedColor } from '$lib/myUtils';
	import tinycolor from 'tinycolor2';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { getPreferredTitle } from '$lib/utils/titleHelper';

	export let episode;
	export let lazyLoad = false;
	export let isVertical;
	export let isDragging = false;
	export let preferRomaji;
	let countdown;
	let isHovered = false;
	let titleColor = '#ffffff';
	let imgSrc = lazyLoad ? '' : episode.image;
	let imageLoaded = false;

	function handleImageLoad() {
		imageLoaded = true;
	}

	function isMobileDevice() {
		return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
	}

	async function extractDominantColor(imgSrc) {
		const img = new Image();
		img.crossOrigin = 'Anonymous';

		try {
			await new Promise((resolve, reject) => {
				img.onload = resolve;
				img.onerror = reject;
				img.src = imgSrc;
			});

			const canvas = document.createElement('canvas');
			canvas.width = 50;
			canvas.height = 50;
			const ctx = canvas.getContext('2d');
			ctx.drawImage(img, 0, 0, 50, 50);

			const imageData = ctx.getImageData(0, 0, 50, 50).data;
			let r = 0,
				g = 0,
				b = 0,
				count = 0;

			for (let i = 0; i < imageData.length; i += 16) {
				const alpha = imageData[i + 3];
				if (alpha >= 125) {
					r += imageData[i];
					g += imageData[i + 1];
					b += imageData[i + 2];
					count++;
				}
			}

			if (count === 0) return '#ffffff';

			const color = tinycolor({
				r: Math.round(r / count),
				g: Math.round(g / count),
				b: Math.round(b / count)
			});

			// let adjustedColor = color.saturate(10);
			let adjustedColor = color;
			while (adjustedColor.getBrightness() < 190) {
				adjustedColor = adjustedColor.lighten(10);
			}
			adjustedColor = adjustedColor.saturate(100);
			// while (adjustedColor.getBrightness() > 180) {
			// 	adjustedColor = adjustedColor.darken(10);
			// }

			return adjustedColor.toHexString();
		} catch (error) {
			console.error(`Error extracting color: ${error}`);
			return '#ffffff';
		}
	}

	async function getDominantColor(imgSrc) {
		const cachedColor = getCachedColor(imgSrc);
		if (cachedColor) {
			titleColor = cachedColor;
			return;
		}

		if (isMobileDevice()) {
			titleColor = '#ffffff';
			return;
		}

		const color = await extractDominantColor(imgSrc);
		titleColor = color;
		setCachedColor(imgSrc, color);
	}

	function updateCardWidth(isVertical) {
		const root = document.documentElement;
		const width = isVertical ? '165px' : '190px';
		root.style.setProperty('--card-width-vertical', width);
	}

	function calculateTimeLeft() {
		const airing_in = episode.airing_in;
		if (airing_in > 0) {
			const days = Math.floor(airing_in / (1000 * 60 * 60 * 24));
			const hours = Math.floor((airing_in / (1000 * 60 * 60)) % 24);
			const minutes = Math.floor((airing_in / 1000 / 60) % 60);
			const seconds = Math.floor((airing_in / 1000) % 60);
			return `${days}d ${hours}g ${minutes}m ${seconds}s`;
		}
		return 'Wkrótce!';
	}

	function handleKeyDown(event) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			window.location.href = generateAnimeUrl(episode);
		}
	}

	function handleDragStart(e) {
		e.preventDefault();
	}

	$: if (isVertical !== undefined) {
		updateCardWidth(isVertical);
	}

	onMount(async () => {
		if (browser) {
			await getDominantColor(episode.image);
		}
		updateCardWidth(isVertical);

		// Calculate the airing time using the same rounding logic as ReleaseScheduleContainer
		const airingDate = new Date(Date.now() + episode.airing_in);
		const minutes = airingDate.getMinutes();

		// Apply the same rounding logic as in formatTime() in ReleaseScheduleContainer
		if (minutes >= 45) {
			// Round up to next hour
			airingDate.setHours(airingDate.getHours() + 1);
			airingDate.setMinutes(0);
		} else if (minutes >= 15 && minutes < 45) {
			// Round to half hour
			airingDate.setMinutes(30);
		} else {
			// Round down to current hour
			airingDate.setMinutes(0);
		}
		airingDate.setSeconds(0);
		airingDate.setMilliseconds(0);

		// Recalculate airing_in based on the rounded time
		episode.airing_in = airingDate.getTime() - Date.now() - 3600000;

		countdown = calculateTimeLeft();
		const timer = setInterval(() => {
			episode.airing_in -= 1000;
			countdown = calculateTimeLeft();
		}, 1000);

		return () => clearInterval(timer);
	});
</script>

<a
	href={generateAnimeUrl(episode)}
	class="relative block overflow-visible transition-all duration-300 ease-in-out bg-gray-900 rounded-lg shadow-lg cursor-pointer card-container group hover:z-20 md:hover:scale-105"
	on:mouseenter={() => (isHovered = true)}
	on:mouseleave={() => (isHovered = false)}
	on:mousedown|preventDefault
	on:click={(e) => {
		if (isDragging) {
			e.preventDefault();
		}
	}}
	on:keydown={handleKeyDown}
	on:dragstart={handleDragStart}
	aria-label="Nadchodzący odcinek {episode.episode} {episode.title}, emisja za {countdown}"
>
	<div class="relative w-full" style="aspect-ratio: 12 / 5;">
		{#if !imageLoaded}
			<Skeleton class="absolute inset-0 w-full h-full rounded-t-lg" />
		{/if}
		<img
			src={imgSrc}
			data-src={lazyLoad ? episode.image : null}
			alt="Okładka {episode.title}"
			class="absolute top-0 left-0 object-cover w-full h-full transition-opacity duration-300 rounded-t-lg block-interaction"
			style="opacity: {imageLoaded ? 1 : 0};"
			loading={lazyLoad ? 'lazy' : 'eager'}
			on:load={handleImageLoad}
		/>
		<div class="absolute inset-0 bg-linear-to-t from-gray-900/20 to-transparent"></div>

		<div class="absolute inset-0 flex items-start justify-between p-2 transition-opacity duration-300" style="opacity: {imageLoaded ? 1 : 0}">
			<span class="p-1 rounded-full bg-white/20 hover:bg-white/30" aria-hidden="true">
				<Hourglass size={16} class="text-white transition-colors duration-300" style="color: {isHovered ? titleColor : '#ffffff'}" />
			</span>
			<span class="px-2 py-1 text-xs font-bold transition-colors duration-300 rounded-sm bg-gray-800/80" style="color: {isHovered ? titleColor : '#ffffff'}">
				{countdown}
			</span>
		</div>
	</div>

	<div class="px-2 py-3 space-y-1 fade-in">
		<h3 class="text-sm font-bold transition-colors duration-300 line-clamp-1" style="color: {isHovered ? titleColor : 'white'};">
			{getPreferredTitle(episode, preferRomaji)}
		</h3>
		<p class="hidden text-xs text-gray-400 line-clamp-1 md:flex">{getPreferredTitle(episode, !preferRomaji)}</p>
		<div class="flex items-center justify-between text-xs text-gray-400">
			<div class="flex items-center">
				<Film size={12} class="mr-1" aria-label="Numer odcinka" />
				<span>Odcinek {episode.next_episode}</span>
			</div>
			<div class="flex items-center">
				<Clapperboard size={12} class="mr-1" aria-label="Całkowita liczba odcinków" />
				<span>/{episode.total_episodes ? episode.total_episodes : '?'}</span>
			</div>
		</div>
		<div class="items-center justify-between hidden text-xs text-gray-500 md:flex">
			<div class="flex items-center">
				<Tv size={12} class="mr-1" aria-label="Typ anime" />
				<span>{episode.format}</span>
			</div>
			<div class="flex items-center">
				<Calendar size={12} class="mr-1" aria-label="Rok wydania" />
				<span>{episode.season_year}</span>
			</div>
		</div>
	</div>
</a>

<style>
	:root {
		--card-width-base: 190px;
		--card-width-vertical: 190px;
	}

	.block-interaction {
		pointer-events: none;
	}

	.card-container {
		width: var(--card-width-vertical);
		transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
	}

	@media (min-width: 450px) {
		.card-container {
			--card-width-base: 230px;
			--card-width-vertical: 230px;
		}
	}

	@media (min-width: 530px) {
		.card-container {
			--card-width-base: 240px;
			--card-width-vertical: 240px;
		}
	}

	@media (min-width: 640px) {
		.card-container {
			--card-width-base: 240px;
			--card-width-vertical: 240px;
		}
	}

	@media (min-width: 1024px) {
		.card-container {
			--card-width-base: 300px;
			--card-width-vertical: 300px;
		}
	}

	@media (min-width: 1537px) {
		.card-container {
			--card-width-base: 300px;
			--card-width-vertical: 300px;
		}
	}

	.fade-in {
		animation: fadeIn 300ms ease-in-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}
</style>
