import { json } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';
import { ADMIN_USERS } from '$lib/constants/adminUsers';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Use the centralized admin users list
const PERMITTED_USER_IDS = ADMIN_USERS;

export async function DELETE({ request, locals }) {
    const { session, user } = await locals.safeGetSession();
    if (!session || !user || !PERMITTED_USER_IDS.includes(user.id)) {
        throw error(401, 'Unauthorized');
    }

    const { commentId } = await request.json();

    const { error: deleteError } = await supabase
        .from('comments')
        .delete()
        .eq('id', commentId);

    if (deleteError) throw error(500, 'Failed to delete comment');

    return json({ success: true });
}
