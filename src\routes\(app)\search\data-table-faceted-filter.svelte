<script>
	import { PlusCircle, Check } from 'lucide-svelte';
	import * as Popover from '$lib/components/ui/popover';
	import { Button } from '$lib/components/ui/button';
	import { Separator } from '$lib/components/ui/separator';
	import { Badge } from '$lib/components/ui/badge';
	import * as Command from '$lib/components/ui/command';

	export let filterValues = [];
	export let title = '';
	export let options = [];
	export let counts = {};
	export let onFilterChange = (values) => {};
	export let isLoading = false;

	let open = false;
	let searchTerm = '';

	const genreTranslations = {
		Action: 'Akcja',
		Adventure: 'Przygodowe',
		Comedy: 'Komedia',
		Drama: 'Dramat',
		Fantasy: 'Fantasy',
		Horror: 'Horror',
		Mystery: 'Mystery',
		Romance: 'Romans',
		'Sci-Fi': 'Sci-Fi',
		'Slice of Life': 'Slice of Life',
		Sports: 'Sport',
		Supernatural: 'Nadprzyrodzone',
		Thriller: 'Thriller',
		Mecha: 'Mecha',
		Psychological: 'Psychologiczne',
		Music: 'Muzyczne',
		School: 'Szkolne',
		Seinen: 'Seinen',
		Shounen: 'Shounen',
		Shoujo: 'Shoujo',
		'Martial Arts': 'Sztuki Walki',
		Historical: 'Historyczne',
		Military: 'Militarne',
		Demons: 'Demony',
		Magic: 'Magia',
		Harem: 'Harem',
		Ecchi: 'Ecchi',
		Isekai: 'Isekai',
		Game: 'Gry',
		Parody: 'Parodia',
		Police: 'Policyjne',
		Space: 'Kosmos',
		Vampire: 'Wampiry'
	};

	const sourceTranslations = {
		MANGA: 'Manga',
		LIGHT_NOVEL: 'Light Novel',
		ORIGINAL: 'Oryginalne',
		GAME: 'Gra',
		VISUAL_NOVEL: 'Visual Novel',
		OTHER: 'Inne'
	};

	const seasonTranslations = {
		WINTER: 'Zima',
		SPRING: 'Wiosna',
		SUMMER: 'Lato',
		FALL: 'Jesień'
	};

	const formatTranslations = {
		MOVIE: 'Film',
		TV: 'TV',
		TV_SHORT: 'TV Short',
		ONA: 'ONA',
		SPECIAL: 'Specjalne'
	};

	const statusTranslations = {
		RELEASING: 'Emitowane',
		FINISHED: 'Zakończone'
	};

	function getTranslationMap(title) {
		switch (title.toLowerCase()) {
			case 'gatunki':
				return genreTranslations;
			case 'status':
				return statusTranslations;
			case 'format':
				return formatTranslations;
			case 'sezon':
				return seasonTranslations;
			case 'źródło':
				return sourceTranslations;
			default:
				return {};
		}
	}

	function translate(value, translationMap) {
		return translationMap[value] || value;
	}

	function getTranslatedValue(value) {
		const translationMap = getTranslationMap(title);
		return translate(value, translationMap);
	}

	$: filteredOptions = searchTerm
		? options.filter((option) => option.label.toLowerCase().includes(searchTerm.toLowerCase()) || getTranslatedValue(option.value).toLowerCase().includes(searchTerm.toLowerCase()))
		: options;

	async function handleSelect(currentValue) {
		// Define which filter types should be single-selection
		const singleSelectionFilters = ['Źródło', 'Sezon', 'Rok', 'Format', 'Status'];

		let newFilterValues;

		if (singleSelectionFilters.includes(title)) {
			// If already selected, remove it (toggle off)
			if (filterValues.includes(currentValue)) {
				newFilterValues = [];
			} else {
				// Replace with new selection
				newFilterValues = [currentValue];
			}
		} else {
			// For genres, allow multiple selections (toggle behavior)
			newFilterValues = filterValues.includes(currentValue) ? filterValues.filter((value) => value !== currentValue) : [...filterValues, currentValue];
		}

		await onFilterChange(newFilterValues);
	}

	async function handleClearFilters() {
		await onFilterChange([]);
		open = false;
		searchTerm = '';
	}

	function handleSearch(event) {
		searchTerm = event.detail;
	}
</script>

<Popover.Root bind:open>
	<Popover.Trigger asChild let:builder>
		<Button builders={[builder]} variant="outline" size="sm" class="hover:bg-muted/50 h-8 cursor-pointer border-dashed" aria-label="Filtruj według {title}" disabled={isLoading}>
			<PlusCircle class="mr-2 h-4 w-4" />
			{title}
			{#if filterValues.length > 0}
				<Separator orientation="vertical" class="mx-2 h-4" />
				<Badge variant="secondary" class="rounded-sm px-1 font-normal lg:hidden">
					{filterValues.length}
				</Badge>
				<div class="hidden space-x-1 lg:flex">
					{#each filterValues.slice(0, 2) as value}
						<Badge variant="secondary" class="rounded-sm px-1 font-normal">
							{getTranslatedValue(value)}
						</Badge>
					{/each}
					{#if filterValues.length > 2}
						<Badge variant="secondary" class="rounded-sm px-1 font-normal">
							+{filterValues.length - 2}
						</Badge>
					{/if}
				</div>
			{/if}
		</Button>
	</Popover.Trigger>
	<Popover.Content class="w-[200px] p-0" align="start">
		<Command.Root>
			<Command.Input placeholder={`Szukaj ${title.toLowerCase()}...`} on:input={(e) => (searchTerm = e.currentTarget.value)} value={searchTerm} disabled={isLoading} />
			<Command.List>
				<Command.Empty>Nie znaleziono wyników.</Command.Empty>
				<Command.Group>
					{#each filteredOptions as option}
						<Command.Item value={option.value} onSelect={() => handleSelect(option.value)} class="flex cursor-pointer items-center justify-between px-2 py-1.5" disabled={isLoading}>
							<div class="flex items-center">
								<div class="mr-2 flex h-4 w-4 items-center justify-center">
									{#if filterValues.includes(option.value)}
										<Check class="h-4 w-4" />
									{/if}
								</div>
								<span>{option.label}</span>
							</div>
							{#if counts[option.value] !== undefined}
								<span class="text-muted-foreground ml-auto flex min-w-[2rem] items-center justify-end font-mono text-xs">
									{counts[option.value]}
								</span>
							{/if}
						</Command.Item>
					{/each}
				</Command.Group>
				{#if filterValues.length > 0}
					<Command.Separator class="my-1" />
					<Command.Item onSelect={handleClearFilters} class="text-muted-foreground cursor-pointer justify-center px-2 py-1.5 text-sm" disabled={isLoading}>Wyczyść filtry</Command.Item>
				{/if}
			</Command.List>
		</Command.Root>
	</Popover.Content>
</Popover.Root>

<style>
	:global(.cmd-empty) {
		padding: 0.75rem;
		text-align: center;
		font-size: 0.875rem;
		color: var(--muted-foreground);
	}

	:global(.cmd-group) {
		padding: 0.5rem;
		overflow-y: auto;
		max-height: 300px;
	}

	:global(.cmd-separator) {
		height: 1px;
		background-color: var(--border);
		margin: 0.5rem 0;
	}

	:global(.cmd-item) {
		position: relative;
		display: flex;
		align-items: center;
		width: 100%;
		padding: 0.375rem 0.5rem;
		font-size: 0.875rem;
		border-radius: 0.25rem;
		outline: none;
		transition: background-color 0.2s;
	}

	:global(.cmd-item:hover:not(:disabled)) {
		background-color: var(--muted);
	}

	:global(.cmd-item[data-selected='true']:not(:disabled)) {
		background-color: var(--muted);
	}

	:global(.cmd-item:disabled) {
		opacity: 0.5;
		cursor: not-allowed;
	}
</style>
