//src/routes/auth/anilist
import { redirect, error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { ANILIST_CLIENT_ID, ANILIST_CLIENT_SECRET } from '$env/static/private';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY, AUTH_PASSWORD_SECRET } from '$env/static/private';
import crypto from 'crypto';
import { View } from 'lucide-svelte';

const supabaseAdmin = createClient(
    PUBLIC_SUPABASE_URL,
    SUPABASE_SERVICE_ROLE_KEY,
    {
        auth: {
            autoRefreshToken: false,
            persistSession: false
        }
    }
);

export const GET = async ({ url, locals }) => {
    const code = url.searchParams.get('code');
    console.log('Starting auth callback with code:', code?.substring(0, 10) + '...');

    if (!code) {
        console.error('No authorization code provided in URL params');
        throw error(400, 'Missing authorization code');
    }

    try {
        // Token exchange with AniList
        console.log('Exchanging code for token...');
        const tokenResponse = await fetch('https://anilist.co/api/v2/oauth/token', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                grant_type: 'authorization_code',
                client_id: ANILIST_CLIENT_ID,
                client_secret: ANILIST_CLIENT_SECRET,
                redirect_uri: `${url.origin}/auth/anilist`,
                code
            })
        });

        const tokenData = await tokenResponse.json();

        if (!tokenResponse.ok) {
            console.error('Token exchange failed:', tokenData);
            throw error(tokenResponse.status, 'Failed to exchange token');
        }

        // Get AniList user info
        console.log('Fetching user info...');
        const userResponse = await fetch('https://graphql.anilist.co', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${tokenData.access_token}`,
            },
            body: JSON.stringify({
                query: `query { Viewer { id name avatar { large } } }`
            })
        });

        const userData = await userResponse.json();
        const { data: { Viewer } } = userData;
        console.log('Got user info for:', Viewer.name);

        const email = `${Viewer.name}-${Viewer.id}-<EMAIL>`;
        const password = crypto.createHash('sha256')
            .update(`${Viewer.id}-${AUTH_PASSWORD_SECRET}`)
            .digest('hex');

        // First check if a user with this profile_id already exists
        console.log('Checking if user with AniList ID exists:', Viewer.id);
        const { data: existingProfile, error: profileQueryError } = await supabaseAdmin
            .from('profiles')
            .select('id')
            .eq('profile_id', Viewer.id)
            .single();

        if (profileQueryError && profileQueryError.code !== 'PGRST116') {
            console.error('Error checking for existing profile:', profileQueryError);
            throw error(500, 'Failed to check for existing user');
        }

        // If we found a profile with this AniList ID
        if (existingProfile) {
            console.log('Found existing profile with AniList ID:', Viewer.id);

            // Get the auth user associated with this profile
            const { data: existingUser, error: userQueryError } = await supabaseAdmin.auth.admin.getUserById(existingProfile.id);

            if (userQueryError) {
                console.error('Error fetching existing user:', userQueryError);
                throw error(500, 'Failed to fetch existing user');
            }

            // Update the user's metadata (including potentially changed username)
            await supabaseAdmin.auth.admin.updateUserById(existingProfile.id, {
                user_metadata: {
                    ...existingUser.user.user_metadata,
                    id: Viewer.id,
                    name: Viewer.name,
                    avatar: Viewer.avatar.large,
                    anilist_token: tokenData.access_token,
                    anilist_refresh_token: tokenData.refresh_token,
                    provider: 'anilist',
                    token_expiry: new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
                }
            });

            // Update profile name if it changed
            await supabaseAdmin
                .from('profiles')
                .update({
                    name: Viewer.name,
                    avatar: Viewer.avatar.large,
                    updated_at: new Date().toISOString()
                })
                .eq('id', existingProfile.id);

            // Sign in the user with their credentials
            const { data: signInData, error: signInError } = await locals.supabase.auth.signInWithPassword({
                email: existingUser.user.email,
                password
            });

            if (signInError) {
                console.error('Failed to sign in existing user:', signInError);
                throw error(500, 'Failed to sign in');
            }

            locals.session = signInData.session;
            locals.user = signInData.user;
            console.log('Existing user signed in successfully');
            throw redirect(303, '/');
        }

        // If no existing profile found, try to sign in with email/password as fallback
        console.log('No profile found with AniList ID, attempting to sign in by email...');
        const { data: signInData, error: signInError } = await locals.supabase.auth.signInWithPassword({
            email,
            password
        });

        if (!signInError && signInData.user) {
            console.log('Existing user signed in successfully by email');

            // Update existing user's token
            await supabaseAdmin.auth.admin.updateUserById(signInData.user.id, {
                user_metadata: {
                    ...signInData.user.user_metadata,
                    id: Viewer.id,
                    name: Viewer.name,
                    avatar: Viewer.avatar.large,
                    anilist_token: tokenData.access_token,
                    anilist_refresh_token: tokenData.refresh_token,
                    provider: 'anilist',
                    token_expiry: new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
                }
            });

            locals.session = signInData.session;
            locals.user = signInData.user;
            throw redirect(303, '/');
        }

        // Double-check one more time that a profile with this ID doesn't exist
        // This is a safety check to prevent duplicate profiles
        const { data: doubleCheckProfile, error: doubleCheckError } = await supabaseAdmin
            .from('profiles')
            .select('id')
            .eq('profile_id', Viewer.id)
            .maybeSingle();

        if (doubleCheckError) {
            console.error('Error during double-check for existing profile:', doubleCheckError);
            throw error(500, 'Failed to verify user does not exist');
        }

        if (doubleCheckProfile) {
            console.error('Profile with this AniList ID already exists, but sign-in failed:', Viewer.id);
            throw error(409, 'A user with this AniList ID already exists but sign-in failed. Please contact support.');
        }

        // If sign in failed, create new user with admin client
        console.log('User not found, creating new account...');
        const { data: newUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
            email,
            password,
            email_confirm: true,
            user_metadata: {
                id: Viewer.id,
                name: Viewer.name,
                avatar: Viewer.avatar.large,
                anilist_token: tokenData.access_token,
                anilist_refresh_token: tokenData.refresh_token,
                provider: 'anilist',
                token_expiry: new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
            }
        });

        if (createError) {
            console.error('Failed to create user:', createError);
            throw error(500, 'Failed to create user account');
        }

        const { error: profileError } = await supabaseAdmin
            .from('profiles')
            .insert({
                id: newUser.user.id,
                profile_id: Viewer.id,
                name: Viewer.name,
                avatar: Viewer.avatar.large,
                role: 'user'
            });

        if (profileError) {
            console.error('Failed to create profile:', profileError);
            // Clean up the created user since profile creation failed
            await supabaseAdmin.auth.admin.deleteUser(newUser.user.id);
            throw error(500, 'Failed to create user profile');
        }

        // Sign in the new user
        const { data: newSignInData, error: newSignInError } = await locals.supabase.auth.signInWithPassword({
            email,
            password
        });

        if (newSignInError) {
            console.error('Failed to sign in new user:', newSignInError);
            throw error(500, 'Failed to sign in');
        }

        locals.session = newSignInData.session;
        locals.user = newSignInData.user;

        console.log('New user created and signed in successfully');
        throw redirect(303, '/');

    } catch (err) {
        // Check if this is a redirect to home page (successful case)
        if (err.constructor.name === 'Redirect' && err.status === 303 && err.location === '/') {
            throw err;
        }

        // Log and handle other errors
        console.error('Auth flow failed:', err);
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
        throw redirect(303, `/?error=auth_failed&message=${encodeURIComponent(JSON.stringify(errorMessage))}`);
    }
};