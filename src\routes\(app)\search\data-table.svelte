<script>
	import { writable } from 'svelte/store';
	import { onMount } from 'svelte';
	import InfiniteLoading from 'svelte-infinite-loading';
	import DataTableToolbar from './data-table-toolbar.svelte';
	import { Loader2, Star } from 'lucide-svelte';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { fade, fly } from 'svelte/transition';
	import tinycolor from 'tinycolor2';
	import ColorThief from 'colorthief';
	import { getCachedColor, setCachedColor, generateAnimeUrl } from '$lib/myUtils';
	import { getPreferredTitle } from '$lib/utils/titleHelper';

	export let data;
	export let currentFilters;
	export let filterCounts;
	export let preferRomaji;

	let currentPage = 1;
	let isLoading = writable(false);
	let isLoadingMore = writable(false);
	let filterCountsStore = writable(filterCounts);
	let hasMore = true;

	const PAGE_SIZE = 12;
	let infiniteKey = 0;
	let isMobileView = false;
	let visibleData = writable(data);

	$: {
		visibleData.set(data);
	}

	let dominantColors = {};

	function formatAiringDate(timestamp) {
		if (!timestamp) return '';

		// Convert UNIX timestamp to Date object
		const airingDate = new Date(timestamp * 1000);

		// Format the date
		const dateOptions = {
			day: 'numeric',
			month: 'long',
			hour: '2-digit',
			minute: '2-digit'
		};
		return airingDate.toLocaleDateString('pl-PL', dateOptions);
	}

	function formatDistance(targetTimestamp) {
		if (!targetTimestamp) return '';

		const now = Date.now();
		const difference = targetTimestamp - now;

		// Return empty string if the date is in the past
		if (difference < 0) return '';

		const seconds = Math.floor(difference / 1000);
		const minutes = Math.floor(seconds / 60);
		const hours = Math.floor(minutes / 60);
		const days = Math.floor(hours / 24);

		if (days > 0) {
			if (days === 1) return '1 dzień';
			if (days % 10 >= 2 && days % 10 <= 4 && (days % 100 < 10 || days % 100 >= 20)) {
				return `${days} dni`;
			}
			return `${days} dni`;
		}

		if (hours > 0) {
			if (hours === 1) return '1 godzina';
			if (hours % 10 >= 2 && hours % 10 <= 4 && (hours % 100 < 10 || hours % 100 >= 20)) {
				return `${hours} godziny`;
			}
			return `${hours} godzin`;
		}

		if (minutes > 0) {
			if (minutes === 1) return '1 minuta';
			if (minutes % 10 >= 2 && minutes % 10 <= 4 && (minutes % 100 < 10 || minutes % 100 >= 20)) {
				return `${minutes} minuty`;
			}
			return `${minutes} minut`;
		}

		if (seconds === 1) return '1 sekunda';
		if (seconds % 10 >= 2 && seconds % 10 <= 4 && (seconds % 100 < 10 || seconds % 100 >= 20)) {
			return `${seconds} sekundy`;
		}
		return `${seconds} sekund`;
	}

	async function extractColorFromImage(imgSrc) {
		return new Promise((resolve) => {
			const img = new Image();
			img.crossOrigin = 'Anonymous';
			img.src = imgSrc;
			img.onload = () => {
				const colorThief = new ColorThief();
				const palette = colorThief.getColor(img);
				let extractedColor = tinycolor(`rgb(${palette[0]}, ${palette[1]}, ${palette[2]})`);
				let adjustedColor = tinycolor(extractedColor).saturate(100);

				while (adjustedColor.getBrightness() < 128) {
					adjustedColor = adjustedColor.lighten(23);
				}

				resolve(adjustedColor.toHexString());
			};
		});
	}

	async function getDominantColor(imgSrc) {
		const cachedColor = getCachedColor(imgSrc);
		if (cachedColor) {
			return cachedColor;
		}

		const color = await extractColorFromImage(imgSrc);
		setCachedColor(imgSrc, color);
		return color;
	}

	async function loadDominantColors() {
		for (let anime of $visibleData) {
			if (!dominantColors[anime.id] && anime.poster) {
				dominantColors[anime.id] = await getDominantColor(anime.poster);
			}
		}
		dominantColors = dominantColors; // Trigger reactivity
	}

	$: {
		if ($visibleData.length > 0) {
			loadDominantColors();
		}
	}

	function getTextColor(backgroundColor) {
		const color = tinycolor(backgroundColor);
		return color.isLight() ? '#000000' : '#FFFFFF';
	}

	// Modify the loadMoreData function
	async function loadMoreData({ detail: { loaded, complete, error } }) {
		try {
			if (!hasMore || $isLoadingMore) {
				complete();
				return;
			}

			isLoadingMore.set(true);

			// Add a small delay to prevent immediate loading
			await new Promise((resolve) => setTimeout(resolve, 100));

			const nextPage = currentPage + 1;
			const response = await fetch(`/api/search?page=${nextPage}&pageSize=${PAGE_SIZE}&${new URLSearchParams(currentFilters).toString()}`);
			const newData = await response.json();

			if (newData.error) {
				throw new Error(newData.error);
			}

			// Update data only after successful fetch
			visibleData.update((current) => [...current, ...newData.data]);
			hasMore = newData.hasMore;
			currentPage = nextPage;

			if (hasMore) {
				loaded();
			} else {
				complete();
			}
		} catch (err) {
			console.error('Error loading more data:', err);
			error();
		} finally {
			isLoadingMore.set(false);
		}
	}

	async function handleLoadMore() {
		if (!hasMore || $isLoadingMore) return;

		try {
			isLoadingMore.set(true);
			const nextPage = currentPage + 1;

			// Create params and add preferRomaji
			const params = new URLSearchParams(new URLSearchParams(currentFilters));
			params.set('preferRomaji', preferRomaji.toString());

			const response = await fetch(`/api/search?page=${nextPage}&pageSize=${PAGE_SIZE}&${params.toString()}`);
			const newData = await response.json();

			if (newData.error) throw new Error(newData.error);

			visibleData.update((current) => [...current, ...newData.data]);
			hasMore = newData.hasMore;
			currentPage = nextPage;
		} catch (err) {
			console.error('Error loading more data:', err);
		} finally {
			isLoadingMore.set(false);
		}
	}

	async function resetInfiniteLoading() {
		currentPage = 1;
		hasMore = true;
		isLoading.set(true);

		try {
			// Create params and add preferRomaji
			const params = new URLSearchParams(new URLSearchParams(currentFilters));
			params.set('preferRomaji', preferRomaji.toString());

			const response = await fetch(`/api/search?page=1&pageSize=${PAGE_SIZE}&${params.toString()}`);
			const newData = await response.json();

			if (newData.error) {
				throw new Error(newData.error);
			}

			visibleData.set(newData.data);
			filterCountsStore.set(newData.filterCounts); // Add this line
			hasMore = newData.hasMore;
			infiniteKey++;
		} catch (error) {
			console.error('Error resetting data:', error);
			visibleData.set([]);
			filterCountsStore.set({}); // Add this line
		} finally {
			isLoading.set(false);
		}
	}

	async function handleFilterUpdate(newFilters) {
		currentFilters = newFilters;
		await resetInfiniteLoading();
	}

	function isMobile() {
		return window.innerWidth <= 640;
	}

	onMount(() => {
		isLoading.set(false);
		isMobileView = isMobile();
		window.addEventListener('resize', () => {
			isMobileView = isMobile();
		});
	});

	function getUniqueKey(anime) {
		return anime.id;
	}

	function getAiringInfo(anime) {
		if (!anime) return 'Nieznane';

		if (anime.nextAiringEpisode) {
			const now = Date.now();
			const airingAt = anime.nextAiringEpisode.airingAt;

			// Check if the airing time is in the past
			if (airingAt < now) {
				return `Odc ${anime.nextAiringEpisode.episode} za 7 dni`;
			} else {
				const timeUntil = formatDistance(anime.nextAiringEpisode.airingAt);
				return `Odc ${anime.nextAiringEpisode.episode} za ${timeUntil}`;
			}
		}

		if (anime.status === 'FINISHED') {
			return `Zakończone (${anime.totalEpisodes} odc)`;
		}

		return anime.status || 'Nieznane';
	}

	let tooltipPosition = 'right';

	function checkTooltipPosition(targetElement) {
		const rect = targetElement.getBoundingClientRect();
		const windowWidth = window.innerWidth;
		const tooltipWidth = 256;

		if (isMobileView) {
			const windowHeight = window.innerHeight;
			const tooltipHeight = 150;
			tooltipPosition = rect.bottom + tooltipHeight > windowHeight ? 'top' : 'bottom';
		} else {
			tooltipPosition = rect.right + tooltipWidth > windowWidth ? 'left' : 'right';
		}
	}

	function getFirstThreeTags(tags) {
		return tags?.slice(0, 3) || [];
	}

	let activeTooltipId = null;
	let tooltipTimeout;

	function showTooltipE(id, event) {
		clearTimeout(tooltipTimeout);
		tooltipTimeout = setTimeout(() => {
			activeTooltipId = id;
			checkTooltipPosition(event.target);
		}, 100);
	}

	function hideTooltipE() {
		clearTimeout(tooltipTimeout);
		tooltipTimeout = setTimeout(() => {
			activeTooltipId = null;
		}, 100);
	}

	function handleKeyDown(event, anime) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			window.location.href = generateAnimeUrl(anime);
		}
	}

	function getAccessibleName(anime) {
		let name = anime.title;
		if (anime.englishTitle && anime.englishTitle !== anime.title) {
			name += `, znane również jako ${anime.englishTitle}`;
		}

		if (anime.nextAiringEpisode) {
			name += `. Odcinek ${anime.nextAiringEpisode.episode} za ${formatDistance(anime.timeUntilNextEpisode)}. `;
		} else {
			name += `. ${anime.status === 'FINISHED' ? 'Zakończone' : anime.status}. `;
		}

		if (anime.rating) {
			name += `Ocena: ${anime.rating * 10}%. `;
		}

		if (anime.studio) {
			name += `Studio: ${anime.studio}. `;
		}

		if (anime.genres && anime.genres.length > 0) {
			name += `Gatunki: ${anime.genres.join(', ')}.`;
		}

		if (anime.totalEpisodes) {
			name += ` ${anime.totalEpisodes} wszystkich odcinków.`;
		}

		return name;
	}

	$: {
		filterCountsStore.set(filterCounts);
	}
</script>

<div class="mx-auto space-y-4 px-2 sm:space-y-8 sm:px-6 md:px-8 lg:px-16">
	<DataTableToolbar {data} onFilterChange={handleFilterUpdate} {currentFilters} filterCounts={$filterCountsStore} isLoading={$isLoading} />
	<div class="grid grid-cols-2 gap-2 sm:grid-cols-3 sm:gap-8 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6" role="grid" aria-label="Lista anime">
		<div role="row" class="contents">
			{#if $isLoading}
				{#each Array(PAGE_SIZE) as _, i}
					<div class="tile group relative" in:fade|local={{ duration: 500 }} out:fade|local={{ duration: 500 }} role="gridcell" aria-label="Ładowanie anime">
						<Skeleton class="aspect-2/3 w-full" />
						<Skeleton class="mt-2 h-4 w-3/4" />
					</div>
				{/each}
			{:else if $visibleData.length === 0}
				<div class="col-span-full py-8 text-center" role="gridcell">Nie znaleziono wyników.</div>
			{:else}
				{#each $visibleData as anime, i (getUniqueKey(anime))}
					<div class="tile group relative" transition:fade|local={{ duration: 500 }} role="gridcell">
						<a
							href={generateAnimeUrl(anime)}
							on:mouseenter={(e) => !isMobileView && showTooltipE(i, e)}
							on:mouseleave={() => !isMobileView && hideTooltipE()}
							on:keydown={(e) => handleKeyDown(e, anime)}
							aria-label={getAccessibleName(anime)}
							tabindex="0"
						>
							<div class="relative aspect-2/3 overflow-hidden">
								{#key anime.poster}
									{#if anime.poster}
										<div class="absolute inset-0 animate-pulse bg-gray-700" transition:fade|local={{ duration: 300 }} />
										<img
											src={anime.poster}
											alt=""
											class="absolute inset-0 h-full w-full object-cover opacity-0 transition-opacity duration-300"
											on:load={(e) => {
												e.target.classList.remove('opacity-0');
												e.target.classList.add('opacity-100');
											}}
										/>
									{/if}
								{/key}
							</div>
							<div class="mt-2" transition:fade|local={{ duration: 300, delay: 200 }}>
								<h3 class="line-clamp-2 text-xs font-semibold sm:text-sm">
									{getPreferredTitle(anime, preferRomaji)}
								</h3>
							</div>
						</a>

						{#if activeTooltipId === i}
							<div
								class="absolute z-50 {isMobileView ? 'w-full' : 'w-64'}"
								class:left-0={isMobileView}
								class:right-full={!isMobileView && tooltipPosition === 'left'}
								class:left-full={!isMobileView && tooltipPosition === 'right'}
								class:bottom-full={isMobileView && tooltipPosition === 'top'}
								class:top-full={isMobileView && tooltipPosition === 'bottom'}
								class:top-0={!isMobileView}
								class:mb-2={isMobileView && tooltipPosition === 'top'}
								class:mt-2={isMobileView && tooltipPosition === 'bottom'}
								class:mr-2={!isMobileView && tooltipPosition === 'left'}
								class:ml-2={!isMobileView && tooltipPosition === 'right'}
								transition:fly={{
									y: isMobileView ? (tooltipPosition === 'top' ? -10 : 10) : 0,
									x: isMobileView ? 0 : tooltipPosition === 'right' ? 10 : -10,
									duration: 200
								}}
								on:mouseenter={() => !isMobileView && hideTooltipE()}
								role="tooltip"
								id={`tooltip-${i}`}
							>
								<div class="rounded-lg bg-gray-800 p-4 text-white opacity-90 shadow-lg" transition:fade|local={{ duration: 150 }}>
									<div class="mb-2 flex items-center justify-between">
										<h4 class="text-xs font-bold sm:text-sm">
											{getAiringInfo(anime)}
										</h4>
										<div class="flex items-center" role="img" aria-label={`Rating: ${anime.rating * 10}%`}>
											<Star class="mr-1 h-3 w-3 text-yellow-400 sm:h-4 sm:w-4" aria-hidden="true" />
											<span class="text-xs sm:text-sm">{(anime.rating * 10).toFixed(0)}%</span>
										</div>
									</div>
									<p class="mb-1 text-xs text-gray-300 sm:text-sm">{anime.studio}</p>
									<p class="mb-2 text-xs text-gray-300 sm:text-sm">{anime.format}</p>
									<div class="flex flex-wrap gap-1 sm:gap-2" role="list" aria-label="Genres">
										{#each getFirstThreeTags(anime.genres) as tag}
											<span
												class="rounded-full px-2 py-1 text-[10px] sm:text-xs"
												style="background-color: {dominantColors[anime.id] || '#6B7280'}; color: {getTextColor(dominantColors[anime.id] || '#6B7280')};"
												role="listitem"
											>
												{tag}
											</span>
										{/each}
									</div>
								</div>
							</div>
						{/if}
					</div>
				{/each}
			{/if}
		</div>
	</div>

	{#if $visibleData.length > 0}
		{#if $isLoadingMore}
			<div class="col-span-full flex items-center justify-center py-8">
				<Loader2 class="h-8 w-8 animate-spin" aria-hidden="true" />
				<span class="sr-only">Ładowanie kolejnych wyników</span>
			</div>
		{/if}
	{/if}

	{#if hasMore && !$isLoading}
		<div class="flex justify-center py-8">
			<button
				on:click={handleLoadMore}
				class="rounded-md bg-[#ee8585] px-4 py-2 text-sm font-medium text-white hover:bg-[#8ec3f4] focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-hidden disabled:opacity-50"
				disabled={$isLoadingMore}
			>
				{#if $isLoadingMore}
					<Loader2 class="mr-2 inline h-4 w-4 animate-spin" />
					Ładowanie...
				{:else}
					Załaduj więcej
				{/if}
			</button>
		</div>
	{:else if !hasMore && $visibleData.length > 0}
		<div class="py-8 text-center text-sm text-gray-400">Brak kolejnych wyników.</div>
	{/if}
</div>

<style>
	:global(.infinite-status-prompt) {
		display: none !important;
	}

	:root {
		--tile-width: 150px;
	}

	.tile {
		width: var(--tile-width);
		max-width: 100%;
		margin: 0 auto;
	}

	.line-clamp-2 {
		display: -webkit-box;
		line-clamp: 2;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	@media (min-width: 640px) {
		:root {
			--tile-width: 180px;
		}
	}

	@media (min-width: 768px) {
		:root {
			--tile-width: 200px;
		}
	}

	@media (min-width: 1024px) {
		:root {
			--tile-width: 220px;
		}
	}

	@media (min-width: 1280px) {
		:root {
			--tile-width: 240px;
		}
	}

	:global(.infinite-status-prompt) {
		display: none !important;
	}

	.animate-pulse {
		animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	}

	@keyframes pulse {
		0%,
		100% {
			opacity: 1;
		}
		50% {
			opacity: 0.5;
		}
	}

	:global(.fade-enter) {
		opacity: 0;
	}

	:global(.fade-enter-active) {
		opacity: 1;
		transition: opacity 300ms ease-in;
	}

	:global(.fade-exit) {
		opacity: 1;
	}

	:global(.fade-exit-active) {
		opacity: 0;
		transition: opacity 300ms ease-out;
	}
</style>
