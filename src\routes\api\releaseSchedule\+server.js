//routes/api/releaseSchedule/+server.js
import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function POST({ request }) {
  const { date } = await request.json();
  const requestDate = new Date(date);

  // Calculate start and end timestamps for the day
  const startOfDay = new Date(requestDate);
  startOfDay.setHours(0, 0, 0, 0);
  const endOfDay = new Date(requestDate);
  endOfDay.setHours(23, 59, 59, 999);

  // Convert to timestamps in milliseconds
  const startTimestamp = startOfDay.getTime();
  const endTimestamp = endOfDay.getTime();

  // Call the weekly schedule RPC function but filter for just one day
  const { data, error } = await supabase.rpc('get_release_schedule', {
    start_timestamp: startTimestamp,
    end_timestamp: endTimestamp
  });

  if (error) {
    console.error('Error fetching release schedule:', error);
    throw error;
  }

  return json(data);
}