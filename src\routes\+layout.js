import { createBrowserClient, createServer<PERSON>lient, isBrowser } from '@supabase/ssr';
import { PUBLIC_SUPABASE_ANON_KEY, PUBLIC_SUPABASE_URL } from '$env/static/public';
import { userStore } from '$lib/stores/userLogin.js';
import posthog from 'posthog-js';
import { browser } from '$app/environment';
import { cacheKeys, getCachedData, setCachedData } from '$lib/utils/cacheUtils';

export const load = async ({ data, depends, fetch }) => {
  depends('supabase:auth');

  if (browser) {
    posthog.init('phc_LDx4Y8JTRxynhJKW3qRXMHxD4QKNvqKA4k68ir7HR3j', {
      api_host: 'https://banana.lycoris.cafe/',
      ui_host: 'https://eu.posthog.com/',
      capture_pageview: false,
      capture_pageleave: false,
      enable_recording_console_log: true,
      enable_heatmaps: false,
      mask_all_text: false,
      person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
      autocapture: false,
      capture_pageview: 'history_change'
    });
  }

  const supabase = isBrowser()
    ? createBrowserClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
      global: { fetch },
    })
    : createServerClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
      global: { fetch },
      cookies: {
        getAll() {
          return data.cookies;
        },
      },
    });

  const safeGetSession = async () => {
    const {
      data: { session }
    } = await supabase.auth.getSession();
    if (!session) {
      return { session: null, user: null };
    }

    const {
      data: { user },
      error
    } = await supabase.auth.getUser();
    if (error) {
      return { session: null, user: null };
    }

    userStore.login(user)
    return { session, user };
  };

  async function fetchUserSettings(user) {
    if (!browser || !user) return null;

    // Define a cache key for user settings
    const userSettingsCacheKey = `${cacheKeys.USER_SETTINGS}`;

    // Try to get settings from cache first
    const cachedSettings = getCachedData(userSettingsCacheKey);
    if (cachedSettings) {
      return cachedSettings;
    }

    try {
      const response = await fetch('/api/user/settings');
      if (response.ok) {
        const userSettings = await response.json();

        // Cache the settings
        setCachedData(userSettingsCacheKey, userSettings);

        return userSettings;
      }
    } catch (error) {
      console.error('Error fetching user settings:', error);
    }

    return null;
  }

  const { session, user } = await safeGetSession();
  const userSettings = await fetchUserSettings(user);

  return { session, supabase, user, userSettings };
};