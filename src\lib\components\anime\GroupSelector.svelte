<script>
	import { createEventDispatcher, onMount } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import { userStore } from '$lib/stores/userLogin';
	import { Dialog, DialogContent, DialogHeader, Di<PERSON>Title, DialogFooter } from '$lib/components/ui/dialog';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import { toast } from 'svelte-sonner';
	import { fade, scale, slide } from 'svelte/transition';
	import { ADMIN_USERS } from '$lib/constants/adminUsers';

	export let otherGroups = null;
	export let episodeId = null;
	export let isCollapsed = false;
	export let selectedGroup = null;
	export let fullscreenMode = false; // New prop to control fullscreen mode
	export let episode = null; // Add episode prop to access secondarySource

	const dispatch = createEventDispatcher();

	$: isAdmin = $userStore?.role === 'authenticated' && ADMIN_USERS.includes($userStore?.id);

	let editMode = false;
	let showAddGroupDialog = false;
	let showAddPlayerDialog = false;
	let showDeleteConfirmDialog = false;
	let showPlayerSelectDialog = false;
	let hasSecondarySource = false;
	let allGroups = {};
	let newGroupName = '';
	let newPlayerType = '';
	let newPlayerUrl = '';
	let currentGroupName = '';
	let groupToDelete = '';
	let playerToDelete = { groupName: '', index: -1 };
	let deleteType = ''; // 'group' or 'player'
	// We don't need to track unsupported players anymore since all players are directly embedded
	let currentGroupPlayers = [];
	let currentPlayerType = null; // Track the current player type

	// Since we're using direct embedding, we don't need to check if a player is supported anymore
	// All players are considered supported

	// Check if the episode has a secondary source (lycoris.cafe)
	function checkSecondarySource() {
		if (episode && episode.secondarySource && Object.keys(episode.secondarySource).length > 0) {
			hasSecondarySource = true;
			return true;
		}
		return false;
	}

	// Create a combined groups object with lycoris.cafe first
	function createGroupsList() {
		// Save current players for the selected group before updating allGroups
		const savedCurrentGroupPlayers = [...currentGroupPlayers];

		// Start with an empty object
		allGroups = {};

		// Add lycoris.cafe group if secondary source exists
		if (hasSecondarySource) {
			allGroups['lycoris.cafe'] = {
				players: [{ 'lycoris.cafe': episode.secondarySource.FHD || episode.secondarySource.HD || episode.secondarySource.SD }]
			};
		}

		// Add other groups
		if (otherGroups) {
			allGroups = { ...allGroups, ...otherGroups };
		}

		// Update player information if a group is already selected
		if (selectedGroup && allGroups[selectedGroup] && allGroups[selectedGroup].players && allGroups[selectedGroup].players.length > 0) {
			const bestPlayer = getBestPlayer(allGroups[selectedGroup], selectedGroup);
			// All players are now considered supported

			// Set the current player type
			if (bestPlayer && bestPlayer.type) {
				currentPlayerType = bestPlayer.type;
			} else if (selectedGroup === 'lycoris.cafe') {
				currentPlayerType = 'player';
			}

			// Restore the saved players for the current group
			if (savedCurrentGroupPlayers.length > 0) {
				currentGroupPlayers = savedCurrentGroupPlayers;
				console.log('Restored currentGroupPlayers in createGroupsList:', currentGroupPlayers);
			} else if (allGroups[selectedGroup].players.length > 0) {
				// If we didn't have saved players but the group has players, update currentGroupPlayers
				currentGroupPlayers = [...allGroups[selectedGroup].players];
				console.log('Updated currentGroupPlayers in createGroupsList:', currentGroupPlayers);
			}
		}

		return allGroups;
	}

	// Create the groups list but don't pre-select lycoris.cafe
	onMount(() => {
		if (checkSecondarySource()) {
			createGroupsList();

			// Set default player type for lycoris.cafe if it's selected
			if (selectedGroup === 'lycoris.cafe') {
				currentPlayerType = 'player';
			}
		} else {
			allGroups = otherGroups || {};
		}

		// If no player type is set but a group is selected, set a default
		if (!currentPlayerType && selectedGroup) {
			if (selectedGroup === 'lycoris.cafe') {
				currentPlayerType = 'player';
			} else if (allGroups[selectedGroup]?.players?.length > 0) {
				const firstPlayer = allGroups[selectedGroup].players[0];
				currentPlayerType = Object.keys(firstPlayer)[0];
			}
		}
	});

	function selectGroup(groupName) {
		selectedGroup = groupName;

		// Store all players for the current group for the player selection dialog
		if (groupName && allGroups[groupName] && allGroups[groupName].players) {
			currentGroupPlayers = [...allGroups[groupName].players]; // Create a new array to trigger reactivity
			console.log('Setting currentGroupPlayers in selectGroup:', currentGroupPlayers);
		} else {
			currentGroupPlayers = [];
		}

		// Get the best player from the selected group
		let playerInfo = null;

		if (groupName && allGroups[groupName] && allGroups[groupName].players && allGroups[groupName].players.length > 0) {
			const bestPlayer = getBestPlayer(allGroups[groupName], groupName);

			playerInfo = {
				type: bestPlayer.type,
				url: bestPlayer.url
			};

			// Set the current player type
			currentPlayerType = bestPlayer.type;
		}

		// All players are now considered supported since we're using direct embedding

		dispatch('select', {
			group: groupName,
			playerInfo
		});

		// Automatically collapse the group list after selection
		isCollapsed = true;
	}

	// Function to select a specific player from the current group
	function selectSpecificPlayer(playerType, playerUrl) {
		const playerInfo = {
			type: playerType,
			url: playerUrl
		};

		// Update the current player type
		currentPlayerType = playerType;

		// All players are now considered supported since we're using direct embedding

		dispatch('select', {
			group: selectedGroup,
			playerInfo
		});

		// Close the player selection dialog
		showPlayerSelectDialog = false;

		// Automatically collapse the group list after selection
		isCollapsed = true;
	}

	// This function is kept for backward compatibility but is no longer needed
	// since all players are now considered supported with direct embedding
	function isPlayerSupported(playerType, groupName = null) {
		return true;
	}

	function toggleCollapse() {
		isCollapsed = !isCollapsed;
	}

	function toggleEditMode() {
		editMode = !editMode;
	}

	async function saveGroups() {
		try {
			const response = await fetch('/api/anime/update-groups', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					episodeId,
					otherGroups: otherGroups
				})
			});

			if (!response.ok) {
				throw new Error('Failed to update groups');
			}

			toast.success('Grupy zostały zaktualizowane');

			// If the currently selected group was removed, reset selection
			if (selectedGroup && !otherGroups[selectedGroup]) {
				selectedGroup = null;
				dispatch('select', {
					group: null,
					playerInfo: null
				});
			}

			// Recreate the groups list with the updated groups
			createGroupsList();
		} catch (error) {
			console.error('Error updating groups:', error);
			toast.error('Nie udało się zaktualizować grup');
		}
	}

	function openAddGroupDialog() {
		newGroupName = '';
		showAddGroupDialog = true;
	}

	function openAddPlayerDialog(groupName) {
		currentGroupName = groupName;
		newPlayerType = '';
		newPlayerUrl = '';
		showAddPlayerDialog = true;
	}

	function addNewGroup() {
		if (!newGroupName.trim()) {
			toast.error('Nazwa grupy nie może być pusta');
			return;
		}

		if (otherGroups && otherGroups[newGroupName.trim()]) {
			toast.error('Grupa o tej nazwie już istnieje');
			return;
		}

		// Initialize otherGroups if it doesn't exist
		if (!otherGroups) {
			otherGroups = {};
		}

		// Add the new group
		otherGroups = {
			...otherGroups,
			[newGroupName.trim()]: {
				players: []
			}
		};

		// Update the allGroups to include the new group
		createGroupsList();

		newGroupName = '';
		showAddGroupDialog = false;

		// Save the changes to the server
		saveGroups();
	}

	function addNewPlayer() {
		if (!newPlayerType.trim()) {
			toast.error('Typ playera nie może być pusty');
			return;
		}

		if (!newPlayerUrl.trim()) {
			toast.error('URL playera nie może być pusty');
			return;
		}

		if (!otherGroups || !otherGroups[currentGroupName]) {
			toast.error('Grupa nie istnieje');
			return;
		}

		const player = {};
		player[newPlayerType.trim()] = newPlayerUrl.trim();

		// Create a new players array with the new player
		const updatedPlayers = [...otherGroups[currentGroupName].players, player];

		// Update the group with the new players array
		otherGroups = {
			...otherGroups,
			[currentGroupName]: {
				...otherGroups[currentGroupName],
				players: updatedPlayers
			}
		};

		// Update the allGroups to include the new player
		createGroupsList();

		newPlayerType = '';
		newPlayerUrl = '';
		showAddPlayerDialog = false;

		// Save the changes to the server
		saveGroups();
	}

	function confirmDeleteGroup(groupName) {
		groupToDelete = groupName;
		deleteType = 'group';
		showDeleteConfirmDialog = true;
	}

	function confirmDeletePlayer(groupName, playerIndex) {
		playerToDelete = { groupName, index: playerIndex };
		deleteType = 'player';
		showDeleteConfirmDialog = true;
	}

	function executeDelete() {
		if (deleteType === 'group' && groupToDelete) {
			// Create a new object without the deleted group
			const updatedGroups = { ...otherGroups };
			delete updatedGroups[groupToDelete];
			otherGroups = updatedGroups;

			// If the currently selected group was removed, reset selection
			if (selectedGroup === groupToDelete) {
				selectedGroup = null;
				dispatch('select', {
					group: null,
					playerInfo: null
				});
			}
		} else if (deleteType === 'player' && playerToDelete.groupName && playerToDelete.index >= 0) {
			// Create a new players array without the deleted player
			const updatedPlayers = [...otherGroups[playerToDelete.groupName].players];
			updatedPlayers.splice(playerToDelete.index, 1);

			// Update the group with the new players array
			otherGroups = {
				...otherGroups,
				[playerToDelete.groupName]: {
					...otherGroups[playerToDelete.groupName],
					players: updatedPlayers
				}
			};

			// If this was the last player in the group and it's the selected group,
			// we should update the player info
			if (updatedPlayers.length === 0 && selectedGroup === playerToDelete.groupName) {
				dispatch('select', {
					group: selectedGroup,
					playerInfo: null
				});
			}
		}

		// Update the allGroups
		createGroupsList();

		// Reset delete state
		groupToDelete = '';
		playerToDelete = { groupName: '', index: -1 };
		deleteType = '';
		showDeleteConfirmDialog = false;

		// Save the changes to the server
		saveGroups();
	}

	// Get the first player from a group
	function getBestPlayer(group, groupName = null) {
		if (!group || !group.players || !group.players.length) return null;

		// Get the first player from the group
		const firstPlayer = group.players[0];
		const playerType = Object.keys(firstPlayer)[0];
		return {
			type: playerType,
			url: firstPlayer[playerType]
		};
	}

	function getFirstPlayerUrl(group) {
		if (group && group.players && group.players.length > 0) {
			const firstPlayer = group.players[0];
			return Object.values(firstPlayer)[0];
		}
		return null;
	}
</script>

{#if (allGroups && Object.keys(allGroups).length > 0) || (otherGroups && Object.keys(otherGroups).length > 0)}
	<div class="group-selector {fullscreenMode ? 'flex flex-col justify-center px-3 py-4 sm:px-8 sm:py-12' : 'max-h-[min(400px,60vh)] overflow-y-auto'} mb-4 rounded-md bg-gray-800 p-2 shadow-lg transition-all duration-300 sm:p-4">
		{#if isCollapsed}
			<div class="flex flex-col items-start justify-between gap-2 xs:flex-row xs:items-center xs:gap-0" transition:slide={{ duration: 300 }}>
				<div class="flex flex-wrap items-center">
					{#if selectedGroup && (allGroups[selectedGroup] || otherGroups?.[selectedGroup])}
						<div class="flex items-center mr-2">
							<div class="flex items-center justify-center w-8 h-8 mr-2 overflow-hidden bg-gray-800 rounded-full">
								<img src="/android-chrome-192x192.png" alt="{selectedGroup} logo" class="object-cover w-full h-full" />
							</div>
							<span class="font-medium text-white">{selectedGroup}</span>
						</div>
						<div class="flex flex-wrap items-center gap-2 mt-1 xs:mt-0">
							<span class="px-2 py-1 text-xs text-white bg-blue-900 rounded">HD</span>
							<div class="flex items-center">
								<div class="relative h-4 mr-1 w-7">
									<img src="/jp.svg" alt="Japanese" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
								</div>
								<span class="text-sm text-white">JP</span>
							</div>
							<div class="flex items-center">
								<div class="relative h-4 mr-1 w-7">
									<img src="/pl.svg" alt="Polish" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
								</div>
								<span class="text-sm text-white">PL</span>
							</div>
						</div>
					{:else}
						<span class="font-medium text-white">Dostępne inne grupy</span>
					{/if}
				</div>
				<div class="flex justify-end w-full gap-2 mt-2 xs:w-auto xs:mt-0">
					<!-- Player selection dropdown will appear here if a group is selected -->
					{#if selectedGroup && currentGroupPlayers && currentGroupPlayers.length > 0}
						<DropdownMenu.Root>
							<DropdownMenu.Trigger asChild let:builder>
								<Button variant="outline" size="sm" builders={[builder]} class="flex-1 xs:flex-none hover:cursor-pointer hover:bg-gray-600">
									{currentPlayerType || (selectedGroup === 'lycoris.cafe' ? 'lycoris.cafe' : 'Wybierz player')}
								</Button>
							</DropdownMenu.Trigger>
							<DropdownMenu.Content class="text-white bg-gray-800 border-gray-700">
								{#each currentGroupPlayers as player}
									{#each Object.entries(player) as [playerType, playerUrl]}
										<DropdownMenu.Item on:click={() => selectSpecificPlayer(playerType, playerUrl)} class="cursor-pointer hover:bg-gray-700 focus:bg-gray-700 {currentPlayerType === playerType ? 'bg-gray-700' : ''}">
											{playerType}
										</DropdownMenu.Item>
									{/each}
								{/each}
							</DropdownMenu.Content>
						</DropdownMenu.Root>
					{/if}
					<Button variant="outline" size="sm" on:click={toggleCollapse} class="flex-1 xs:flex-none hover:cursor-pointer hover:bg-gray-600">Pokaż grupy</Button>
				</div>
			</div>
		{:else}
			<div transition:slide={{ duration: 300 }}>
				{#if fullscreenMode}
					<h2 class="mb-3 text-xl font-bold text-center text-white sm:mb-6 sm:text-2xl">Wybierz grupę tłumaczeniową</h2>
				{/if}
				<div class="flex flex-col items-start justify-between gap-2 mb-3 xs:flex-row xs:items-center">
					<h3 class="text-lg font-semibold text-white">
						{#if editMode && isAdmin}
							Edytuj grupy
						{:else}
							Wybierz grupę
						{/if}
					</h3>
					<div class="flex justify-end w-full gap-2 xs:w-auto">
						{#if isAdmin}
							<Button variant={editMode ? 'default' : 'outline'} size="sm" on:click={toggleEditMode} class="flex-1 xs:flex-none hover:cursor-pointer hover:bg-gray-600">
								{editMode ? 'Zakończ edycję' : 'Edytuj grupy'}
							</Button>
						{/if}
						{#if !fullscreenMode}
							<Button variant="outline" size="sm" on:click={toggleCollapse} class="flex-1 xs:flex-none hover:cursor-pointer hover:bg-gray-600">Ukryj</Button>
						{/if}
					</div>
				</div>

				<div class="max-h-[min(300px,50vh)] overflow-y-auto">
					<!-- Mobile card view for xs screens -->
					<div class="space-y-2 sm:hidden">
						{#each Object.entries(hasSecondarySource ? allGroups : otherGroups || {}) as [groupName, groupData], index}
							<div role="button" tabindex="0" class="group overflow-hidden rounded-md transition-all duration-300 {selectedGroup === groupName ? 'bg-[#8ec3f4]/80' : 'bg-gray-700 hover:bg-gray-600'}" on:click={() => !editMode && selectGroup(groupName)} on:keydown={(e) => e.key === 'Enter' && !editMode && selectGroup(groupName)}>
								<div class="p-2 border-b border-gray-800 sm:p-3">
									<div class="flex items-center">
										<div class="flex items-center justify-center w-8 h-8 mr-2 overflow-hidden bg-gray-800 rounded-full sm:mr-3 sm:h-10 sm:w-10">
											<img src="/android-chrome-192x192.png" alt="{groupName} logo" class="object-cover w-full h-full" />
										</div>
										<div class="flex-1">
											<span class="text-base font-medium text-white sm:text-lg">{groupName}</span>
										</div>

										<!-- Actions for mobile -->
										{#if editMode && isAdmin}
											<div class="flex gap-1 ml-auto">
												{#if groupName !== 'lycoris.cafe'}
													<Button
														variant="outline"
														size="sm"
														on:click={(e) => {
															e.stopPropagation();
															openAddPlayerDialog(groupName);
														}}
														class="px-2 text-xs hover:cursor-pointer"
													>
														Dodaj
													</Button>
													<Button
														variant="destructive"
														size="sm"
														on:click={(e) => {
															e.stopPropagation();
															confirmDeleteGroup(groupName);
														}}
														class="px-2 text-xs hover:cursor-pointer"
													>
														Usuń
													</Button>
												{/if}
											</div>
										{:else}
											<Button
												variant={selectedGroup === groupName ? 'default' : 'outline'}
												size="sm"
												class="ml-auto hover:cursor-pointer {selectedGroup === groupName ? 'bg-blue-900 text-white hover:bg-blue-800' : ''}"
												on:click={(e) => {
													e.stopPropagation();
													selectGroup(groupName);
												}}
												aria-label="Wybierz grupę {groupName}"
												aria-pressed={selectedGroup === groupName}
											>
												{selectedGroup === groupName ? 'Wybrano' : 'Wybierz'}
											</Button>
										{/if}
									</div>

									<!-- Mobile info row -->
									<div class="flex items-center gap-1 mt-1 sm:mt-2 sm:gap-2">
										<span class="rounded bg-blue-900 px-1 py-0.5 text-xs text-white sm:px-2 sm:py-1">HD</span>
										<div class="flex items-center">
											<div class="relative w-5 h-3 mr-1 sm:h-4 sm:w-7">
												<img src="/jp.svg" alt="Japanese" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
											</div>
											<span class="text-xs text-white">JP</span>
										</div>
										<div class="flex items-center">
											<div class="relative w-5 h-3 mr-1 sm:h-4 sm:w-7">
												<img src="/pl.svg" alt="Polish" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
											</div>
											<span class="text-xs text-white">PL</span>
										</div>
									</div>
								</div>

								<!-- Show players in edit mode for mobile -->
								{#if editMode && isAdmin && groupName !== 'lycoris.cafe' && groupData.players && groupData.players.length > 0}
									{#each groupData.players as player, playerIndex}
										<div class="p-3 pl-6 bg-gray-800 border-b border-gray-700">
											<div class="flex items-start justify-between">
												<div class="flex-1 pr-2">
													{#each Object.entries(player) as [playerType, playerUrl]}
														<div>
															<span class="font-medium">{playerType}:</span>
															<span class="text-sm text-gray-300 break-all">{playerUrl}</span>
														</div>
													{/each}
												</div>
												<Button
													variant="destructive"
													size="sm"
													on:click={(e) => {
														e.stopPropagation();
														confirmDeletePlayer(groupName, playerIndex);
													}}
													class="px-2 text-xs hover:cursor-pointer"
												>
													Usuń
												</Button>
											</div>
										</div>
									{/each}
								{/if}
							</div>
						{/each}
					</div>

					<!-- Desktop table view -->
					<div class="hidden overflow-x-auto sm:block">
						<table class="w-full border-collapse">
							<thead class="sticky top-0 z-10 bg-gray-900">
								<tr>
									<th class="p-3 font-medium text-left text-gray-400">Grupa</th>
									<th class="p-3 font-medium text-center text-gray-400">Jakość</th>
									<th class="p-3 font-medium text-center text-gray-400">Audio</th>
									<th class="p-3 font-medium text-center text-gray-400">Napisy</th>
									<th class="p-3 font-medium text-right text-gray-400">
										{#if editMode && isAdmin}
											Akcje
										{/if}
									</th>
								</tr>
							</thead>
							<tbody>
								{#each Object.entries(hasSecondarySource ? allGroups : otherGroups || {}) as [groupName, groupData], index}
									<tr class="group transition-all duration-300 hover:cursor-pointer {selectedGroup === groupName ? 'bg-[#8ec3f4]/80' : 'bg-gray-700 hover:bg-gray-600'}" on:click={() => !editMode && selectGroup(groupName)}>
										<!-- Group Logo and Name -->
										<td class="p-3 border-b border-gray-800">
											<div class="flex items-center">
												<div class="flex items-center justify-center w-10 h-10 mr-3 overflow-hidden bg-gray-800 rounded-full">
													<img src="/android-chrome-192x192.png" alt="{groupName} logo" class="object-cover w-full h-full" />
												</div>
												<span class="text-lg font-medium text-white">{groupName}</span>
											</div>
										</td>

										<!-- Quality -->
										<td class="p-3 text-center border-b border-gray-800">
											<span class="inline-block px-2 py-1 text-xs text-white bg-blue-900 rounded"> HD </span>
										</td>

										<!-- Audio Language -->
										<td class="p-3 text-center border-b border-gray-800">
											<div class="flex items-center justify-center">
												<div class="relative h-5 mr-1 w-9">
													<img src="/jp.svg" alt="Japanese" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
												</div>
												<span class="text-sm text-white">JP</span>
											</div>
										</td>

										<!-- Subtitle Language -->
										<td class="p-3 text-center border-b border-gray-800">
											<div class="flex items-center justify-center">
												<div class="relative h-5 mr-1 w-9">
													<img src="/pl.svg" alt="Polish" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
												</div>
												<span class="text-sm text-white">PL</span>
											</div>
										</td>

										<!-- Actions -->
										<td class="p-3 text-right border-b border-gray-800">
											{#if editMode && isAdmin}
												<div class="flex justify-end gap-2">
													{#if groupName !== 'lycoris.cafe'}
														<Button
															variant="outline"
															size="sm"
															on:click={(e) => {
																e.stopPropagation();
																openAddPlayerDialog(groupName);
															}}
															class="hover:cursor-pointer"
														>
															Dodaj player
														</Button>
														<Button
															variant="destructive"
															size="sm"
															on:click={(e) => {
																e.stopPropagation();
																confirmDeleteGroup(groupName);
															}}
															class="hover:cursor-pointer"
														>
															Usuń
														</Button>
													{/if}
												</div>
											{:else}
												<Button
													variant={selectedGroup === groupName ? 'default' : 'outline'}
													size="sm"
													class="hover:cursor-pointer {selectedGroup === groupName ? 'bg-blue-900 text-white hover:bg-blue-800' : ''}"
													on:click={(e) => {
														e.stopPropagation();
														selectGroup(groupName);
													}}
													aria-label="Wybierz grupę {groupName}"
													aria-pressed={selectedGroup === groupName}
												>
													{selectedGroup === groupName ? 'Wybrano' : 'Wybierz'}
												</Button>
											{/if}
										</td>
									</tr>

									<!-- Show players in edit mode -->
									{#if editMode && isAdmin && groupName !== 'lycoris.cafe' && groupData.players && groupData.players.length > 0}
										{#each groupData.players as player, playerIndex}
											<tr class="bg-gray-800">
												<td colspan="4" class="p-3 pl-16 border-b border-gray-700">
													{#each Object.entries(player) as [playerType, playerUrl]}
														<div>
															<span class="font-medium">{playerType}:</span>
															<span class="text-sm text-gray-300 break-all">{playerUrl}</span>
														</div>
													{/each}
												</td>
												<td class="p-3 text-right border-b border-gray-700">
													<Button
														variant="destructive"
														size="sm"
														on:click={(e) => {
															e.stopPropagation();
															confirmDeletePlayer(groupName, playerIndex);
														}}
														class="hover:cursor-pointer"
													>
														Usuń
													</Button>
												</td>
											</tr>
										{/each}
									{/if}
								{/each}
							</tbody>
						</table>
					</div>
				</div>

				{#if editMode && isAdmin}
					<div class="sticky bottom-0 z-10 flex flex-col justify-between gap-2 pt-2 pb-1 mt-4 bg-gray-800 border-t border-gray-700 xs:flex-row xs:gap-0">
						<Button variant="outline" class="w-full xs:w-auto hover:cursor-pointer" on:click={openAddGroupDialog}>Dodaj nową grupę</Button>
						<Button variant="default" class="w-full xs:w-auto hover:cursor-pointer" on:click={saveGroups}>Zapisz zmiany</Button>
					</div>
				{/if}
			</div>
		{/if}
	</div>
{/if}

<!-- Delete Confirmation Dialog -->
<Dialog bind:open={showDeleteConfirmDialog}>
	<DialogContent class="mx-auto w-[95vw] max-w-md border-gray-700 bg-gray-900 p-4 text-white sm:p-6">
		<DialogHeader>
			<DialogTitle>Potwierdź usunięcie</DialogTitle>
		</DialogHeader>
		<div class="py-4">
			{#if deleteType === 'group'}
				<p>Czy na pewno chcesz usunąć grupę "{groupToDelete}"?</p>
				<p class="mt-2 text-sm text-gray-400">Ta operacja jest nieodwracalna.</p>
			{:else if deleteType === 'player'}
				<p>Czy na pewno chcesz usunąć tego playera?</p>
				<p class="mt-2 text-sm text-gray-400">Ta operacja jest nieodwracalna.</p>
			{/if}
		</div>
		<DialogFooter class="flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
			<Button variant="outline" on:click={() => (showDeleteConfirmDialog = false)} class="w-full hover:cursor-pointer sm:w-auto">Anuluj</Button>
			<Button variant="destructive" on:click={executeDelete} class="w-full hover:cursor-pointer sm:w-auto">Usuń</Button>
		</DialogFooter>
	</DialogContent>
</Dialog>

<!-- Add Group Dialog -->
<Dialog bind:open={showAddGroupDialog}>
	<DialogContent class="mx-auto w-[95vw] max-w-md border-gray-700 bg-gray-900 p-4 text-white sm:p-6">
		<DialogHeader>
			<DialogTitle>Dodaj nową grupę</DialogTitle>
		</DialogHeader>
		<div class="py-4">
			<label for="newGroupName" class="block mb-1 text-sm font-medium">Nazwa grupy</label>
			<input id="newGroupName" type="text" bind:value={newGroupName} class="w-full p-3 text-white bg-gray-800 border border-gray-700 rounded-md" placeholder="Nazwa grupy" on:keydown={(e) => e.key === 'Enter' && addNewGroup()} />
		</div>
		<DialogFooter class="flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
			<Button variant="outline" on:click={() => (showAddGroupDialog = false)} class="w-full hover:cursor-pointer sm:w-auto">Anuluj</Button>
			<Button variant="default" on:click={addNewGroup} class="w-full hover:cursor-pointer sm:w-auto">Dodaj</Button>
		</DialogFooter>
	</DialogContent>
</Dialog>

<!-- Add Player Dialog -->
<Dialog bind:open={showAddPlayerDialog}>
	<DialogContent class="mx-auto w-[95vw] max-w-md border-gray-700 bg-gray-900 p-4 text-white sm:p-6">
		<DialogHeader>
			<DialogTitle>Dodaj nowy player</DialogTitle>
			<p class="text-sm text-gray-400">Dodaj player dla grupy: {currentGroupName}</p>
		</DialogHeader>
		<div class="py-4 space-y-4">
			<div>
				<label for="playerType" class="block mb-1 text-sm font-medium">Typ playera</label>
				<input id="playerType" type="text" bind:value={newPlayerType} class="w-full p-3 text-white bg-gray-800 border border-gray-700 rounded-md" placeholder="np. sibnet, cda, vk" />
			</div>
			<div>
				<label for="playerUrl" class="block mb-1 text-sm font-medium">URL playera</label>
				<input id="playerUrl" type="text" bind:value={newPlayerUrl} class="w-full p-3 text-white bg-gray-800 border border-gray-700 rounded-md" placeholder="https://..." />
			</div>
		</div>
		<DialogFooter class="flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
			<Button variant="outline" on:click={() => (showAddPlayerDialog = false)} class="w-full hover:cursor-pointer sm:w-auto">Anuluj</Button>
			<Button variant="default" on:click={addNewPlayer} class="w-full hover:cursor-pointer sm:w-auto">Dodaj</Button>
		</DialogFooter>
	</DialogContent>
</Dialog>

<style>


	/* Ensure touch targets are large enough on mobile */
	@media (max-width: 480px) {
		[role='button'] {
			min-height: 40px;
		}

		input {
			min-height: 40px;
		}
	}
</style>
