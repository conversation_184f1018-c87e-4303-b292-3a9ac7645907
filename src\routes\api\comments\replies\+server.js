import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function getAllReplies(parentIds) {
  if (!parentIds || !parentIds.length) return [];

  const { data: replies, error } = await supabase
    .from('comments')
    .select(`
      *,
      anime:anime_metadata(
        anilist_id,
        romaji_title
      )
    `)
    .in('parent_id', parentIds);

  if (error) throw error;
  if (!replies.length) return [];

  // Recursively get deeper replies
  const deeperReplies = await getAllReplies(replies.map(r => r.id));

  return [...replies, ...deeperReplies].map(reply => ({
    ...reply,
    anime_title: reply.anime?.romaji_title
  }));
}

export async function GET({ url }) {
  const parentIds = url.searchParams.get('parentIds')?.split(',');

  if (!parentIds || !parentIds.length) {
    return json({ replies: [] });
  }

  try {
    const replies = await getAllReplies(parentIds);
    return json({ replies });
  } catch (error) {
    console.error('Error fetching replies:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}