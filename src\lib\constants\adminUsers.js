/**
 * Admin user IDs for authorization checks
 * Centralized here to make updates easier
 */
export const ADMIN_USERS = [
  'dcb189fb-005d-409c-a7cc-9ece546f98fe',
  '6e7a7fed-2c0c-4fb5-9495-7830fe3ad0fb'
];

/**
 * Check if a user ID is an admin
 * @param {string} userId - The user ID to check
 * @returns {boolean} - True if the user is an admin, false otherwise
 */
export function isAdmin(userId) {
  return ADMIN_USERS.includes(userId);
}
