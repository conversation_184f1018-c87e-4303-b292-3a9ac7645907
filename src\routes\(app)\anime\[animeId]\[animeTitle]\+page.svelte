<script>
	//src/routes/anime/[animeId]/[animeTitle]/+page.svelte
	import { page } from '$app/stores';
	import { get } from 'svelte/store';
	import { pl } from 'date-fns/locale';
	import { onMount, afterUpdate, onDestroy } from 'svelte';
	import { formatDistanceToNow } from 'date-fns';
	import { fade } from 'svelte/transition';
	import { Button } from '$lib/components/ui/button';
	import { Play, Share, ExternalLink, Languages, TrendingUp, Clock, X, CircleEllipsis, Ellipsis } from 'lucide-svelte';
	import { Star, Tv, List, CalendarDays, BookOpen } from 'lucide-svelte';
	import { goto, afterNavigate } from '$app/navigation';
	import { normalizeTitle, generateAnimeUrl, getCachedColor, setCachedColor } from '$lib/myUtils';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import InfiniteComments from '$lib/components/containers/comments/InfiniteComments.svelte';
	import AnimeEditDialog from '$lib/components/sections/anime/AnimeEditDialog.svelte';
	import NavbarMobile from '$lib/components/sections/navbar/NavbarMobile.svelte';
	import { browser } from '$app/environment';
	import { userStore } from '$lib/stores/userLogin.js';
	import { progressStore } from '$lib/stores/progressStore';
	import { toast } from 'svelte-sonner';
	import AnimeNavbar from '$lib/components/sections/navbar/AnimeNavbar.svelte';
	import tinycolor from 'tinycolor2';
	import { cacheKeys, getCachedData, setCachedData } from '$lib/utils/cacheUtils';
	import UserProfileModal from '$lib/components/sections/shared/UserProfileModal.svelte';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { MoreVertical, Trash2 } from 'lucide-svelte';
	import { getPreferredTitle } from '$lib/utils/titleHelper';
	import MoreModal from '$lib/components/sections/shared/MoreModal.svelte';

	$: isLoggedIn = $userStore ? $userStore.role === 'authenticated' : false;
	const provider = $userStore.user_metadata?.provider;
	import AnimeEditLoginPrompt from '$lib/components/sections/anime/AnimeEditLoginPrompt.svelte';

	// Add state for the Stremio modal
	let showStremioModal = false;

	function handleCloseStremioModal() {
		showStremioModal = false;
	}

	const genreTranslations = {
		Action: 'Akcja',
		Adventure: 'Przygodowe',
		Comedy: 'Komedia',
		Drama: 'Dramat',
		Fantasy: 'Fantasy',
		Horror: 'Horror',
		Mystery: 'Mystery',
		Romance: 'Romans',
		'Sci-Fi': 'Sci-Fi',
		'Slice of Life': 'Slice of Life',
		Sports: 'Sport',
		Supernatural: 'Nadprzyrodzone',
		Thriller: 'Thriller',
		Mecha: 'Mecha',
		Psychological: 'Psychologiczne',
		Music: 'Muzyczne',
		School: 'Szkolne',
		Seinen: 'Seinen',
		Shounen: 'Shounen',
		Shoujo: 'Shoujo',
		'Martial Arts': 'Sztuki Walki',
		Historical: 'Historyczne',
		Military: 'Militarne',
		Demons: 'Demony',
		Magic: 'Magia',
		Harem: 'Harem',
		Ecchi: 'Ecchi',
		Isekai: 'Isekai',
		Game: 'Gry',
		Parody: 'Parodia',
		Police: 'Policyjne',
		Space: 'Kosmos',
		Vampire: 'Wampiry'
	};

	const statusTranslations = {
		RELEASING: 'W trakcie emisji',
		FINISHED: 'Zakończone',
		NOT_AIRED: 'Przed premierą',
		CANCELLED: 'Anulowane',
		UPCOMING: 'Nadchodzące',
		NOT_YET_RELEASED: 'Nadchodzące'
	};

	const formatTranslations = {
		MOVIE: 'Film',
		TV: 'TV',
		TV_SHORT: 'TV Short',
		ONA: 'ONA',
		SPECIAL: 'Specjalne'
	};

	const sourceTranslations = {
		MANGA: 'Manga',
		LIGHT_NOVEL: 'Light Novel',
		ORIGINAL: 'Oryginalne',
		VIDEO_GAME: 'Gra',
		VISUAL_NOVEL: 'Visual Novel',
		OTHER: 'Inne'
	};

	const seasonTranslations = {
		WINTER: 'Zima',
		SPRING: 'Wiosna',
		SUMMER: 'Lato',
		FALL: 'Jesień'
	};

	export let data;
	let preferRomaji;
	if (!data.userSettings?.titleLanguage) {
		preferRomaji = true;
	} else {
		preferRomaji = data.userSettings.titleLanguage === 'romaji';
	}
	let anime = data.anime;
	let isLoadingAniListData = true;
	let mounted = false;
	let dominantColor;
	let mainContainer;
	let scrollContainerMain;
	let isComponentMounted = false;
	let showUserModal = false;
	let friendRatings = [];
	$: friendRatings;

	let dialogOpen = false;
	let worker;
	let ogImageUrl = getOGImage();

	const MIN_BRIGHTNESS = 140;
	const MAX_BRIGHTNESS = 180;

	let backgroundImage;
	const ANILIST_API = 'https://graphql.anilist.co';
	const MEDIA_ENTRY_QUERY = `
query ($mediaId: Int, $userId: Int) {
  Media(id: $mediaId) {
    id
    title {
      romaji
      english
    }
  }
  MediaList(userId: $userId, mediaId: $mediaId) {
    id
    status
    score
    progress
    startedAt {
      year
      month
      day
    }
    completedAt {
      year
      month
      day
    }
    repeat
    notes
    updatedAt
  }
  Viewer {
    id
    mediaListOptions {
      scoreFormat
    }
  }
}`;

	function handleLoginPrompt() {
		showUserModal = true;
	}

	function handleCloseModal() {
		showUserModal = false;
	}

	async function clearLocalProgress() {
		const mediaCacheKey = `${cacheKeys.ANILIST_MEDIA}${anime.id}`;
		localStorage.removeItem(mediaCacheKey);
		toast.success('Postęp niezapisany na liście został wyczyszczony.');
		await sleep(1000);
		window.location.reload();
	}

	async function refreshAnilistToken() {
		try {
			const response = await fetch('/api/anilist/refresh-token', {
				method: 'POST'
			});

			if (!response.ok) {
				throw new Error('Failed to refresh token');
			}

			const { access_token } = await response.json();

			// Update user store with new token
			userStore.update((currentUser) => ({
				...currentUser,
				user_metadata: {
					...currentUser.user_metadata,
					anilist_token: access_token
				}
			}));

			return access_token;
		} catch (error) {
			console.error('Error refreshing token:', error);
			toast.error('Nie udało się odświeżyć tokena');
			return null;
		}
	}

	async function fetchAniListMediaData() {
		try {
			const cacheKey = `${cacheKeys.ANILIST_MEDIA}${anime.id}`;
			if (browser) {
				const cachedData = getCachedData(cacheKey);
				if (cachedData) {
					return cachedData;
				}
			}

			let token = $userStore.user_metadata?.anilist_token;
			const userId = $userStore.user_metadata?.id;
			const tokenExpiry = $userStore.user_metadata?.token_expiry;

			if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
				token = await refreshAnilistToken();
				if (!token) {
					throw new Error('Could not refresh token');
				}
			}

			if (!token || !userId) {
				return null;
			}

			const response = await fetch(ANILIST_API, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json',
					Authorization: `Bearer ${token}`
				},
				body: JSON.stringify({
					query: MEDIA_ENTRY_QUERY,
					variables: {
						mediaId: parseInt(anime.id),
						userId: parseInt(userId)
					}
				})
			});

			const data = await response.json();

			if (data.errors && data.data?.MediaList === null) {
				// Normal case - anime not in user's list yet
				return {
					status: 'planning',
					score: 0,
					episodeProgress: 0,
					startDate: null,
					finishDate: null,
					totalRewatches: '',
					notes: ''
				};
			}

			const mediaList = data.data.MediaList;
			if (!mediaList) {
				return {
					status: 'planning',
					score: 0,
					episodeProgress: 0,
					startDate: null,
					finishDate: null,
					totalRewatches: '',
					notes: ''
				};
			}

			const formattedData = {
				status: mediaList.status?.toLowerCase() || 'planning',
				score: mediaList.score || 0,
				episodeProgress: mediaList.progress || 0,
				startDate: mediaList.startedAt?.year ? new Date(mediaList.startedAt.year, mediaList.startedAt.month - 1, mediaList.startedAt.day) : null,
				finishDate: mediaList.completedAt?.year ? new Date(mediaList.completedAt.year, mediaList.completedAt.month - 1, mediaList.completedAt.day) : null,
				totalRewatches: mediaList.repeat || '',
				notes: mediaList.notes || ''
			};

			if (browser) {
				setCachedData(cacheKey, formattedData);
			}

			return formattedData;
		} catch (error) {
			console.error('Error fetching AniList data:', error);
			return null;
		}
	}

	async function fetchMALMediaData() {
		if (!isLoggedIn) return null;

		try {
			const cacheKey = `${cacheKeys.MAL_MEDIA}${anime.id}`;

			// Check cache first
			const cachedData = getCachedData(cacheKey);
			if (cachedData) {
				return cachedData;
			}

			let token = $userStore.user_metadata?.mal_token;
			const tokenExpiry = $userStore.user_metadata?.token_expiry;

			if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
				token = await refreshMALToken();
				if (!token) {
					throw new Error('Could not refresh token');
				}
			}

			if (!token) {
				return null;
			}

			// Use server-side proxy instead of direct MAL API request
			const response = await fetch('/api/mal/proxy/anime-details', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					animeId: anime.mal_id || anime.id,
					token
				})
			});

			if (!response.ok) {
				throw new Error('Failed to fetch MAL data');
			}

			const data = await response.json();
			const myStatus = data.my_list_status;

			if (!myStatus) {
				// New anime not in list yet
				return {
					status: 'planning',
					score: 0,
					episodeProgress: 0,
					startDate: null,
					finishDate: null,
					totalRewatches: '',
					notes: ''
				};
			}

			const formattedData = {
				status: mapMALStatusToAniList(myStatus.status),
				score: myStatus.score || 0,
				episodeProgress: myStatus.num_episodes_watched || 0,
				startDate: myStatus.start_date ? new Date(myStatus.start_date) : null,
				finishDate: myStatus.finish_date ? new Date(myStatus.finish_date) : null,
				totalRewatches: myStatus.num_times_rewatched || '',
				notes: myStatus.comments || ''
			};

			if (browser) {
				setCachedData(cacheKey, formattedData);
			}
			return formattedData;
		} catch (error) {
			console.error('Error fetching MAL data:', error);
			return null;
		}
	}

	function mapMALStatusToAniList(status) {
		const statusMap = {
			watching: 'current',
			completed: 'completed',
			on_hold: 'on-hold',
			dropped: 'dropped',
			plan_to_watch: 'planning'
		};
		return statusMap[status] || 'planning';
	}

	async function refreshMALToken() {
		try {
			const response = await fetch('/api/mal/refresh-token', {
				method: 'POST'
			});

			if (!response.ok) {
				throw new Error('Failed to refresh token');
			}

			const { access_token } = await response.json();

			userStore.update((currentUser) => ({
				...currentUser,
				user_metadata: {
					...currentUser.user_metadata,
					mal_token: access_token
				}
			}));

			return access_token;
		} catch (error) {
			console.error('Error refreshing MAL token:', error);
			toast.error('Nie udało się odświeżyć tokena MAL. Możesz spróbować wylogować i zalogować się ponownie aby naprawić ten błąd.', {
				duration: Number.POSITIVE_INFINITY
			});
			return null;
		}
	}

	async function enrichAnimeWithAniListData() {
		isLoadingAniListData = true;

		try {
			const provider = $userStore?.user_metadata?.provider;
			let data;

			if (provider === 'anilist') {
				data = await fetchAniListMediaData();
			} else if (provider === 'mal') {
				data = await fetchMALMediaData();
			}

			if (data) {
				anime = {
					...anime,
					...data
				};
			}
		} catch (error) {
			console.error('Error enriching anime data:', error);
		} finally {
			isLoadingAniListData = false;
		}
	}

	function getWatchedClass(episodeNumber) {
		// Get AniList progress
		const anilistProgress = anime.status === 'completed' ? anime.totalEpisodes : anime.episodeProgress || 0;
		// Get local progress for current episode
		const localProgress = $progressStore[anime.id]?.[episodeNumber]?.progress || 0;

		// Episode is completed in AniList
		if (episodeNumber <= anilistProgress) {
			return 'opacity-60';
		}

		// Episode is in progress locally
		if (episodeNumber === anilistProgress + 1) {
			return localProgress > 85 ? 'opacity-60' : 'opacity-90';
		}

		// Episode not started
		return 'opacity-90';
	}

	function getPercentageCompletion(episodeNumber) {
		const anilistProgress = anime.status === 'completed' ? anime.totalEpisodes : anime.episodeProgress || 0;
		const localProgress = $progressStore[anime.id]?.[episodeNumber]?.progress || 0;

		// Episode is completed in AniList
		if (episodeNumber <= anilistProgress) {
			return 100;
		}

		if (localProgress > 85) {
			return 100; // Mark as complete if >85%
		}
		return localProgress;
	}

	function enhanceEpisodeData(episodes) {
		// console.log(episodes);
		// console.log(anime);
		const anilistProgress = anime.episodeProgress || 0;

		return episodes.map((episode) => {
			const localProgress = $progressStore[anime.id]?.[episode.number]?.progress || 0;

			const isWatched = episode.number <= anilistProgress;
			const progressPercentage = isWatched ? 100 : episode.number === anilistProgress + 1 ? localProgress : 0;

			return {
				...episode,
				watched: isWatched,
				progress: progressPercentage
			};
		});
	}

	function isMobileDevice() {
		if (!browser) return false;
		let isMobile = window.innerWidth < 640;
		if (isMobile) return true;
		return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
	}

	function getEpisodeStyle(episode) {
		const now = new Date();
		const airDate = new Date(episode.airDate);
		const isReleased = airDate <= now;

		return {
			opacity: isReleased ? (episode.progress === 100 || episode.watched ? '0.65' : '0.9') : '0.4',
			cursor: isReleased ? 'pointer' : 'default',
			pointerEvents: isReleased ? 'auto' : 'none'
		};
	}

	function getEpisodeTimeStatus(airDate, isDelayed = false) {
		// Check if this anime has delayed airing
		if (isDelayed || (anime && anime.delayed_airing)) {
			return 'Poza harmonogramem - jak zrobimy to będzie';
		}

		const episodeDate = new Date(airDate);

		if (episodeDate.toString() === 'Invalid Date') {
			return '';
		}

		const now = new Date();
		if (episodeDate <= now) {
			return formatDistanceToNow(episodeDate - 3600000, { addSuffix: true, locale: pl });
		} else {
			return `Wychodzi ${formatDistanceToNow(episodeDate - 3600000, { addSuffix: true, locale: pl })}`;
		}
	}

	function formatAiringTime(airingAt) {
		if (!airingAt) return 'Unknown';

		const date = new Date(airingAt);
		const days = ['Niedziela', 'Poniedziałek', 'Wtorek', 'Środa', 'Czwartek', 'Piątek', 'Sobota'];
		const dayName = days[date.getDay()];

		// Round up to next hour
		const minutes = date.getMinutes();
		if (minutes > 0) {
			date.setHours(date.getHours() + 1);
		}
		date.setMinutes(0);

		// Format time to HH:00
		const hours = date.getHours().toString().padStart(2, '0');
		return `${dayName} ${hours}:00`;
	}

	function openDialog() {
		if (anime) {
			dialogOpen = true;
		}
	}

	function closeDialog() {
		dialogOpen = false;
	}

	function saveChanges(updatedAnime) {
		if (anime) {
			anime = { ...anime, ...updatedAnime };
			// Invalidate caches
			if (browser) {
				const mediaCacheKey = `${cacheKeys.ANILIST_MEDIA}${updatedAnime.id}`;
				const continueWatchingCacheKeyAL = `${cacheKeys.ANILIST_WATCHING}${updatedAnime.id}`;
				const continueWatchingCacheKeyMAL = `${cacheKeys.MAL_WATCHING}${updatedAnime.mal_id}`;
				const continueWatchingCacheKeyMAL2 = `${cacheKeys.MAL_WATCHING}`;
				let HomeCW = JSON.parse(localStorage.getItem(cacheKeys.HOME_DATA));

				// Find entry of id "anime.id" and update its current_episode
				if (HomeCW && HomeCW.continueWatchingData) {
					HomeCW.continueWatchingData = HomeCW.continueWatchingData.map((item) => {
						if (parseInt(item.id) === parseInt(updatedAnime.id)) {
							return {
								...item,
								current_episode: updatedAnime.episodeProgress,
								updated_at: new Date().toISOString()
							};
						}
						return item;
					});
					localStorage.setItem(cacheKeys.HOME_DATA, JSON.stringify(HomeCW));
				}

				localStorage.removeItem(mediaCacheKey);
				localStorage.removeItem(continueWatchingCacheKeyAL);
				localStorage.removeItem(continueWatchingCacheKeyMAL);
				localStorage.removeItem(continueWatchingCacheKeyMAL2);
			}
		}
		closeDialog();
	}

	function deleteEntry() {
		closeAndReturnHome();
	}

	let navigatingBack = false;

	afterNavigate(({ to, from }) => {
		if (navigatingBack && to && to.url.pathname.includes('/watch')) {
			navigatingBack = false;
			goto('/', {
				replaceState: true
			});
		} else if (from?.params?.animeId !== to?.params?.animeId) {
			window.scrollTo(0, 0);
		}
	});

	$: {
		if ($page.data.anime) {
			anime = $page.data.anime;
		}
	}

	function closeAndReturnHome() {
		navigatingBack = true;
		goto('/');
	}

	async function extractDominantColor(imgSrc) {
		const img = new Image();
		img.crossOrigin = 'Anonymous';

		try {
			await new Promise((resolve, reject) => {
				img.onload = resolve;
				img.onerror = reject;
				img.src = imgSrc;
			});

			const canvas = document.createElement('canvas');
			canvas.width = 50;
			canvas.height = 50;
			const ctx = canvas.getContext('2d');
			ctx.drawImage(img, 0, 0, 50, 50);

			const imageData = ctx.getImageData(0, 0, 50, 50).data;
			let r = 0,
				g = 0,
				b = 0,
				count = 0;

			for (let i = 0; i < imageData.length; i += 16) {
				const alpha = imageData[i + 3];
				if (alpha >= 125) {
					r += imageData[i];
					g += imageData[i + 1];
					b += imageData[i + 2];
					count++;
				}
			}

			if (count === 0) return '#ffffff';

			const color = tinycolor({
				r: Math.round(r / count),
				g: Math.round(g / count),
				b: Math.round(b / count)
			});

			let adjustedColor = color;
			// Ensure brightness is between 140-180
			while (adjustedColor.getBrightness() < 140) {
				adjustedColor = adjustedColor.lighten(5);
			}
			while (adjustedColor.getBrightness() > 180) {
				adjustedColor = adjustedColor.darken(5);
			}

			// Increase saturation
			adjustedColor = adjustedColor.saturate(40);
			return adjustedColor.toHexString();
		} catch (error) {
			console.error(`Error extracting color: ${error}`);
			return '#ffffff';
		}
	}

	async function getDominantColor(imgSrc) {
		const cachedColor = getCachedColor(imgSrc);
		if (cachedColor) {
			dominantColor = cachedColor;
			return;
		}

		if (isMobileDevice()) {
			dominantColor = '#ffffff';
			return;
		}

		dominantColor = await extractDominantColor(imgSrc);
		setCachedColor(imgSrc, dominantColor);
	}

	function sleep(ms) {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	let isMobileLandscape = false;

	function checkMobileLandscape() {
		isMobileLandscape = window.innerWidth <= 9999 && window.innerHeight <= 500;
	}

	async function fetchFriendRatings() {
		if (!isLoggedIn || !anime) return [];

		try {
			const cacheKey = `${cacheKeys.ANILIST_FRIENDS}${anime.id}`;
			if (browser) {
				const cachedData = getCachedData(cacheKey);
				if (cachedData) {
					return cachedData;
				}
			}

			let token = $userStore.user_metadata?.anilist_token;
			const tokenExpiry = $userStore.user_metadata?.token_expiry;

			if (tokenExpiry && new Date(tokenExpiry) <= new Date()) {
				token = await refreshAnilistToken();
				if (!token) {
					throw new Error('Could not refresh token');
				}
			}

			let currentPage = 1;
			let hasNextPage = true;
			let allFriendRatings = [];

			while (hasNextPage) {
				const query = `
        query($id: Int, $page: Int, $perPage: Int) {
          Page(page: $page, perPage: $perPage) {
            pageInfo {
              total
              perPage
              currentPage
              lastPage
              hasNextPage
            }
            mediaList(mediaId: $id, isFollowing: true, sort: UPDATED_TIME_DESC) {
              id
              status
              score
              progress
              user {
                id
                name
                avatar {
                  large
                }
                mediaListOptions {
                  scoreFormat
                }
              }
            }
          }
        }
      `;

				const response = await fetch(ANILIST_API, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						Accept: 'application/json',
						Authorization: `Bearer ${token}`
					},
					body: JSON.stringify({
						query,
						variables: {
							id: parseInt(anime.id),
							page: currentPage,
							perPage: 50
						}
					})
				});

				if (!response.ok) {
					const data = await response.json();
					throw new Error('Failed to fetch friend ratings');
				}

				const data = await response.json();
				if (data.errors) {
					throw new Error(data.errors[0]?.message || 'AniList API returned errors');
				}

				const mediaLists = data.data.Page.mediaList;
				const validRatings = mediaLists
					.filter((list) => list.progress > 0 || list.score > 0)
					.map((list) => ({
						user: {
							name: list.user.name,
							avatar: list.user.avatar
						},
						score: list.score,
						progress: list.progress,
						status: list.status
					}))
					.sort((a, b) => {
						if (b.progress !== a.progress) {
							return b.progress - a.progress;
						}
						return b.score - a.score;
					});

				allFriendRatings.push(...validRatings);
				hasNextPage = data.data.Page.pageInfo.hasNextPage;
				currentPage++;
			}

			if (browser) {
				setCachedData(cacheKey, allFriendRatings);
			}

			return allFriendRatings;
		} catch (error) {
			console.error('Error fetching friend ratings:', error);
			toast.error('Nie udało się pobrać ocen znajomych');
			return [];
		}
	}

	onMount(async () => {
		backgroundImage = anime?.background || (anime?.episodes?.length > 0 ? anime.episodes[anime.episodes.length - 1].thumbnail : null);

		if (isLoggedIn) {
			await enrichAnimeWithAniListData();
			// Only fetch friend ratings if provider is AniList
			if (provider === 'anilist') {
				friendRatings = await fetchFriendRatings();
			}
		}

		checkMobileLandscape();
		window.addEventListener('resize', checkMobileLandscape);
		await sleep(100);
		window.scroll(0, 0);

		if (anime) {
			await getDominantColor(anime.poster);
		}

		isComponentMounted = true;
		mounted = true;
		return () => {
			window.removeEventListener('resize', checkMobileLandscape);
		};
	});

	onDestroy(() => {
		if (scrollContainerMain) {
			scrollContainerMain.removeEventListener('wheel', (e) => handleWheel(e, true));
		}
		if (worker) {
			worker.terminate();
		}
	});

	const exampleRatings = [
		{ name: 'Shadow', avatar: 'https://pixeldrain.com/api/file/DcoddCxN', rating: 8.5 },
		{ name: 'Alpha', avatar: 'https://pixeldrain.com/api/file/qST5f9Py', rating: 7.8 },
		{ name: 'Beta', avatar: 'https://pixeldrain.com/api/file/EB9Hz79Q', rating: 9.0 },
		{ name: 'Gamma', avatar: 'https://pixeldrain.com/api/file/CuM1NLuV', rating: 8.2 },
		{ name: 'Delta', avatar: 'https://pixeldrain.com/api/file/E8up2LoY', rating: 8.1 },
		{ name: 'Epsilon', avatar: 'https://pixeldrain.com/api/file/5efSFEQm', rating: 6.9 },
		{ name: 'Zeta', avatar: 'https://pixeldrain.com/api/file/ZfGfk6L6', rating: 8.0 }
	];

	$: title = anime ? `${anime.englishTitle} - Lycoris` : 'Szczegóły Anime - Lycoris';
	$: description = anime ? `${anime.synopsis?.slice(0, 240).replaceAll('<br>', '')}...` : 'Poznaj szczegóły na lycoris.cafe';
	$: imageUrl = anime ? anime.poster : '';
	$: canonicalUrl = anime ? `https://lycoris.cafe/anime/${anime.id}` : 'https://lycoris.cafe';

	function getOGImage() {
		if (!anime) return null;
		const params = new URLSearchParams({
			id: anime.id
		});

		// Add cache busting
		const cacheBuster = Date.now();
		params.append('_v', cacheBuster);

		return `https://www.lycoris.cafe/api/og?${params.toString()}`;
	}

	$: enhancedEpisodes = anime ? enhanceEpisodeData(anime.episodes || []) : [];
</script>

<svelte:head>
	<!-- Basic metadata -->
	<title>{title}</title>
	<meta name="description" content={description} />

	<!-- OpenGraph tags -->
	<meta name="twitter:title" content={title.length > 50 ? title.substring(0, 50) + '...' : title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={ogImageUrl} />
	<meta property="og:image:width" content="1200" />
	<meta property="og:image:height" content="630" />
	<meta property="og:image:alt" content={`Cover image for ${anime?.title || 'anime'}`} />
	<meta property="og:url" content={canonicalUrl} />
	<meta property="og:type" content="video.tv_show" />
	<meta property="og:site_name" content="Lycoris" />

	<!-- Twitter Card tags -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:title" content={title.length > 50 ? title.substring(0, 50) + '...' : title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={ogImageUrl} />
	<meta name="twitter:image:alt" content={`Cover image for ${anime?.title || 'anime'}`} />

	<!-- Additional metadata for video content -->
	<meta property="video:duration" content={anime.duration || 0} />
	<meta property="video:release_date" content={`${anime.seasonYear}-${anime.season}`} />
	<meta property="video:tag" content={anime.genres.join(',')} />
	<meta property="video:rating" content={anime.rating} />
	<meta property="video:director" content={anime.studio} />

	<!-- Canonical URL -->
	<link rel="canonical" href={canonicalUrl} />

	<!-- Additional SEO metadata -->
	<meta name="keywords" content="anime, {anime?.title || ''}, {anime?.genres?.join(', ') || ''}, watch anime online, Lycoris" />
	<meta name="theme-color" content={'#ee8585'} />
	<meta name="author" content={anime?.studio || 'Lycoris'} />
	<meta name="robots" content="index, follow" />
</svelte:head>

<main class="relative min-h-screen text-white bg-gray-900">
	<div class="fixed z-10 flex items-center top-4 left-4">
		<a href="/" class="flex items-center rounded-full bg-transparent p-1.5 backdrop-blur-sm transition-all duration-200">
			<img src="/android-chrome-192x192.png" alt="lycoris.cafe logo" class="w-8 h-8 rounded-full" />
			<span class="hidden ml-2 text-sm font-medium text-white md:block"> lycoris.cafe </span>
		</a>
	</div>
	{#if anime}
		<div class="relative" in:fade={{ duration: 150, delay: 150 }} out:fade={{ duration: 150 }}>
			<Button class="fixed top-0 right-4 z-10 cursor-pointer rounded-full bg-transparent p-2 text-white hover:bg-gray-700 md:top-[6px] md:right-4" on:click={closeAndReturnHome}>
				<span class="sr-only">Zamknij</span>
				<X size={24} aria-hidden="true" />
			</Button>
			<div class="no-select absolute top-0 right-0 left-0 h-full max-h-[30vh] bg-cover bg-center lg:max-h-[40vh]" style="background-image: url('{backgroundImage}');" aria-hidden="true">
				<div class="absolute inset-0 z-0 bg-black opacity-50"></div>
			</div>

			<div class="relative px-4 pt-[18rem] lg:px-8 lg:pt-[42vh]">
				<div class="flex flex-col lg:flex-row">
					<div class="flex flex-col w-full lg:w-3/5 lg:pr-4">
						<div class="flex flex-col gap-4 lg:flex-row">
							<div class="no-select absolute top-20 left-4 mb-6 w-[150px] shrink-0 lg:relative lg:top-auto lg:left-auto lg:mx-0 lg:-mt-52 lg:mb-6 lg:w-64">
								<img src={anime.poster} alt={`Plakat dla ${anime.title}`} class="w-full h-auto rounded shadow-lg" />
							</div>

							<div class="flex flex-col justify-center grow lg:pl-2">
								<div class="mobile-landscape-wrapper">
									<div class="hidden no-select lg:inline">
										<div class="relative overflow-x-auto">
											<div class="flex gap-2 pt-6 pb-2 pr-8 lg:pt-0">
												{#each anime.genres as genre}
													<span class="px-2 py-1 text-xs bg-gray-800 rounded whitespace-nowrap">{genreTranslations[genre] || genre}</span>
												{/each}
											</div>
										</div>
									</div>
									<h1 class="mt-4 mb-3 line-clamp-3 text-2xl font-bold opacity-90 lg:mt-1 lg:w-[30vw] lg:text-4xl xl:w-[38vw]">
										{getPreferredTitle(anime, preferRomaji)}
									</h1>

									<div class="mb-4 lg:w-[30vw] xl:w-[50vw]">
										<div bind:this={scrollContainerMain} class="relative overflow-x-auto">
											<div class="flex items-center gap-4 pb-2 pr-8 text-sm whitespace-nowrap">
												<div class="flex items-center">
													<Star class="w-4 h-4 mr-1 text-yellow-400 opacity-90" aria-hidden="true" />
													<span class="opacity-80">
														{anime.rating?.toFixed(2)}
														{#if anime.rankings?.raw && Array.isArray(anime.rankings.raw)}
															{@const ratedRanking = anime.rankings.raw.find((item) => item.type === 'RATED' && item.allTime === false && item.season !== null)}
															{#if ratedRanking?.rank}
																<span class="text-gray-400">
																	(#{ratedRanking.rank})
																</span>
															{/if}
														{/if}
													</span>
												</div>
												<div class="flex items-center">
													<TrendingUp class="w-4 h-4 mr-1 text-blue-400 opacity-90" aria-hidden="true" />
													<span class="opacity-80">
														{#if anime.rankings?.raw && Array.isArray(anime.rankings.raw)}
															{@const popularRanking = anime.rankings.raw.find((item) => item.type === 'POPULAR' && item.allTime === false && item.season !== null)}
															{#if popularRanking?.rank}
																#{parseInt(popularRanking.rank)}
															{/if}
														{/if}
														{#if anime.popularity}
															<span class="text-gray-400">({(anime.popularity * 3).toLocaleString()})</span>
														{/if}
													</span>
												</div>
												<div class="flex items-center">
													<Tv class="w-4 h-4 mr-1 text-green-400 opacity-70" aria-hidden="true" />
													<span class="opacity-70">{formatTranslations[anime.format] || anime.format}</span>
												</div>
												<div class="flex items-center">
													<List class="w-4 h-4 mr-1 text-rose-400 opacity-70" aria-hidden="true" />
													<span class="opacity-70">Odcinki: {anime.totalEpisodes ? anime.totalEpisodes : 'nieznane'}</span>
												</div>
											</div>
										</div>
									</div>
									<div class="no-select mb-6 flex gap-2 lg:w-[35vw]">
										<a
											href={`${normalizeTitle(anime)}/watch/${(() => {
												const anilistProgress = anime.status === 'completed' ? anime.totalEpisodes : anime.episodeProgress || 0;
												const nextEpisodeNum = anilistProgress + 1;

												// If the anime is completed or the next episode would exceed total episodes
												if (anime.status === 'completed' || nextEpisodeNum > anime.totalEpisodes) {
													return anime.episodes && anime.episodes.length > 0 ? anime.episodes[0].number : 0;
												}

												// Check if the next episode exists and has aired
												const nextEpisode = anime.episodes.find((ep) => ep.number === nextEpisodeNum);
												if (nextEpisode && new Date(nextEpisode.airDate) <= new Date()) {
													return nextEpisodeNum;
												}

												// If next episode hasn't aired yet, start from beginning
												return anime.episodes && anime.episodes.length > 0 ? anime.episodes[0].number : 0;
											})()}`}
											class="flex h-10 w-1/2 items-center justify-center rounded bg-[#ee8585] px-3 py-2 text-sm font-bold text-black transition-colors duration-300 ease-in-out hover:bg-[#8ec3f4]"
										>
											<span class="flex items-center">
												<Play class="inline-block w-4 h-4 mr-1" aria-hidden="true" />
												Oglądaj teraz
											</span>
										</a>
										<button class="items-center justify-center w-1/2 h-10 px-3 py-2 text-sm font-bold text-white transition-colors duration-300 ease-in-out bg-gray-500 rounded cursor-pointer hover:bg-gray-700 md:w-1/4 xl:inline-flex" on:click={openDialog}>
											<span class="flex items-center justify-center">
												<List class="inline-block w-4 h-4 mr-1 cursor-pointer" aria-hidden="true" />
												Edytuj
											</span>
										</button>

										<DropdownMenu.Root>
											<DropdownMenu.Trigger>
												<button class="items-center justify-center h-10 px-2 py-2 text-sm font-bold text-white transition-colors duration-300 ease-in-out bg-gray-500 rounded cursor-pointer hover:bg-gray-700 md:flex md:w-full lg:flex 2xl:w-40">
													<Ellipsis class="w-4 h-4" />
													<span class="hidden 2xl:flex">&nbsp; Więcej Opcji </span>
												</button>
											</DropdownMenu.Trigger>
											<DropdownMenu.Content class="z-[9999] min-w-[200px] rounded-md border border-gray-700 bg-gray-800 p-1 shadow-md" sideOffset={5}>
												<DropdownMenu.Group>
													<DropdownMenu.Item class="flex cursor-pointer items-center gap-2 rounded p-2 text-sm hover:!bg-gray-700">
														<a href={`https://anilist.co/anime/${anime.id}/`} target="_blank" class="flex items-center w-full gap-2">
															<ExternalLink class="w-4 h-4 text-blue-400" />
															<span>AniList</span>
														</a>
													</DropdownMenu.Item>
													<DropdownMenu.Item class="flex cursor-pointer items-center gap-2 rounded p-2 text-sm hover:!bg-gray-700">
														<a href={`https://myanimelist.net/anime/${anime.mal_id}`} target="_blank" class="flex items-center w-full gap-2">
															<ExternalLink class="w-4 h-4 text-blue-400" />
															<span>MyAnimeList</span>
														</a>
													</DropdownMenu.Item>
													<DropdownMenu.Item class="flex cursor-pointer items-center gap-2 rounded p-2 text-sm hover:!bg-gray-700" on:click={clearLocalProgress}>
														<Trash2 class="w-4 h-4 text-red-400" />
														<span>Wyczyść postęp niezapisany na liście</span>
													</DropdownMenu.Item>
												</DropdownMenu.Group>
											</DropdownMenu.Content>
										</DropdownMenu.Root>
									</div>
								</div>
							</div>
						</div>
						<div class="mb-4">
							<div class="overflow-x-auto custom-scrollbar lg:pb-1">
								<div class="flex gap-4 pb-2 pr-4 text-sm whitespace-nowrap">
									{#if anime.nextAiringEpisode?.airingAt}
										<div class="flex items-center">
											<Clock class="w-4 h-4 mr-2 text-gray-400" aria-hidden="true" />
											<span class="text-gray-300">
												{formatAiringTime(anime.nextAiringEpisode.airingAt - 3600000)}
											</span>
										</div>
										<div class="w-px h-4 bg-gray-600 shrink-0"></div>
									{/if}
									{#if anime.season && anime.seasonYear}
										<div class="flex items-center">
											<CalendarDays class="w-4 h-4 mr-2 text-gray-400" aria-hidden="true" />
											<span class="text-gray-300">{seasonTranslations[anime.season] || anime.season} {anime.seasonYear}</span>
										</div>
										<div class="w-px h-4 bg-gray-600 shrink-0"></div>
									{/if}
									<div class="flex items-center">
										<Tv class="w-4 h-4 mr-2 text-gray-400" aria-hidden="true" />
										<span class="text-gray-300">{statusTranslations[anime.airingStatus] || anime.status}</span>
									</div>
									<div class="w-px h-4 bg-gray-600 shrink-0"></div>
									<div class="flex items-center">
										<BookOpen class="w-4 h-4 mr-2 text-gray-400" aria-hidden="true" />
										<span class="text-gray-300">{sourceTranslations[anime.source] || anime.source}</span>
									</div>
									<div class="w-px h-4 bg-gray-600 shrink-0"></div>
									<div class="flex items-center">
										<Languages class="w-4 h-4 mr-2 text-gray-400" aria-hidden="true" />
										<span class="text-gray-300">
											{getPreferredTitle(anime, !preferRomaji)}
										</span>
									</div>
								</div>
							</div>
						</div>
						<div class="mt-1 mb-4 lg:mt-0">
							<h2 class="mb-2 text-lg font-semibold">Opis</h2>
							<p class="text-sm text-gray-300">{@html anime.synopsis}</p>
						</div>

						<div class="p-4 mb-6 border rounded-lg shadow-md no-select border-gray-700/50 bg-gray-800/70 lg:hidden">
							<div class="flex items-center gap-2">
								<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#ee8585]">
									<path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
								</svg>
								<p class="text-sm text-gray-200">
									Odcinki możesz pobrać na naszym <a href="https://discord.gg/lycoriscafe" data-umami-event="openDiscordDownload" target="_blank" rel="noopener noreferrer" class="font-medium text-[#ee8585] transition-colors hover:text-[#8ec3f4] hover:underline">Discordzie</a>
								</p>
							</div>
						</div>

						<div class="no-select lg:hidden">
							{#each Array(anime.totalEpisodes || 12) as _, i}
								{@const episodeNum = i + 1}
								{@const localProgress = $progressStore[anime.id]?.[episodeNum]?.progress || 0}
								{@const episode = anime.episodes.find((ep) => ep.number === episodeNum) || {
									number: episodeNum,
									title: `Odcinek ${episodeNum}`,
									thumbnail: anime.poster,
									airDate: (() => {
										const existingEpisode = anime.episodes.find((ep) => ep.number === episodeNum);
										if (existingEpisode) {
											return new Date(existingEpisode.airDate);
										}

										if (anime.nextAiringEpisode?.episode === episodeNum) {
											return new Date(anime.nextAiringEpisode.airingAt);
										}

										// Guard against empty episodes array
										if (!anime.episodes.length && !anime.nextAiringEpisode) {
											return new Date(); // Default to current date or another fallback
										}

										const lastKnownEpisode = anime.nextAiringEpisode || (anime.episodes.length > 0 ? anime.episodes[anime.episodes.length - 1] : null);

										// If there's no last known episode, return a default date
										if (!lastKnownEpisode) {
											return new Date();
										}

										const lastKnownDate = new Date(anime.nextAiringEpisode?.airingAt || (anime.episodes.length > 0 ? anime.episodes[anime.episodes.length - 1].airDate : Date.now()));

										const episodesDiff = episodeNum - lastKnownEpisode.episode;
										return new Date(lastKnownDate.getTime() + episodesDiff * 7 * 24 * 60 * 60 * 1000);
									})(),
									progress: 0,
									watched: false
								}}
								{@const isInDatabase = anime.episodes.some((ep) => ep.number === episodeNum)}
								{@const now = new Date()}
								{@const airDate = new Date(episode.airDate)}
								{@const isComingSoon = airDate <= now && !isInDatabase}
								{@const isReleased = airDate <= now && isInDatabase}
								{@const isWatched = episodeNum <= (anime.episodeProgress || 0)}
								{@const progress = isWatched ? 100 : episodeNum === (anime.episodeProgress || 0) + 1 ? localProgress : 0}
								{@const opacity = isComingSoon ? '0.4' : isReleased ? (isWatched || progress > 85 ? '0.65' : '0.9') : '0.4'}

								<a href={isReleased ? `${normalizeTitle(anime)}/watch/${episode.number}` : null} class="episode-card group relative mb-4 block h-20 rounded bg-black shadow-md {isReleased ? 'cursor-pointer' : 'cursor-default'}" style="opacity: {opacity}; pointer-events: {isReleased ? 'auto' : 'none'};">
									<div class="absolute top-0 left-0 w-24 h-full overflow-hidden rounded-l">
										<img src={episode.thumbnail} alt="" class="object-cover w-full h-full opacity-100" />
									</div>
									<div class="pt-2 pr-4 ml-28">
										<h3 class="line-clamp-2 text-sm font-semibold {getWatchedClass(episode.number)}">
											{#if episode.title?.includes(' - ')}
												{episode.number}. {episode.title.split(' - ')[1]}
											{:else if episode.title?.toLowerCase().startsWith('odcinek')}
												{episode.title}
											{:else}
												{episode.number}. {episode.title || `Odcinek ${episode.number}`}
											{/if}
										</h3>
										<div class="w-full h-1 mt-2 bg-gray-700 rounded-full">
											<div class="h-full transition-all duration-200 rounded-full" style="width: {getPercentageCompletion(episode.number)}%; background-color: {dominantColor};" aria-hidden="true"></div>
										</div>
										<span class="absolute text-xs text-gray-400 bottom-2 left-28">
											{isComingSoon ? 'Wkrótce!' : getEpisodeTimeStatus(episode.airDate)}
										</span>
									</div>
								</a>
							{/each}
						</div>
						<div class="mb-0">
							<h2 class="mb-2 text-lg font-semibold">Powiązane tytuły</h2>
							<div class="flex flex-col gap-4">
								{#if anime.relatedEntries && anime.relatedEntries.length > 0}
									{#each anime.relatedEntries as entry}
										<a href={generateAnimeUrl(entry)} class="flex items-center p-2 bg-gray-800 rounded-md">
											<img src={entry.poster} alt="" class="object-cover w-12 h-16 mr-4 rounded no-select" />
											<div>
												<h3 class="text-sm font-semibold">{entry.title}</h3>
												<p class="text-xs text-gray-400">
													{entry.relation} · {entry.type}
												</p>
											</div>
										</a>
									{/each}
								{:else}
									<p class="mb-4 text-sm text-gray-400">Nie znaleziono powiązanych tytułów</p>
								{/if}
							</div>
						</div>

						<div class="{anime.relatedEntries && anime.relatedEntries.length > 0 ? 'mt-8' : 'mt-0'} flex flex-col lg:flex-row">
							<div class="w-full px-0 md:ml-[-8px] lg:w-[70%]">
								<h2 class="sr-only">Komentarze</h2>
								<InfiniteComments comments={[]} pageSize={5} context="anime" animeId={anime.id} smHeight="300px" xlHeight="600px" lgHeight="600px" mdHeight="500px" padding={4} enforceMaxDepth={true} />
							</div>

							<div class="w-full pt-6 md:pt-0 md:pl-6 lg:w-[40%]">
								<h2 class="sr-only">Oceny znajomych</h2>
								<div class="relative mb-8">
									{#if !isLoggedIn}
										<div class="absolute inset-0 z-10 flex flex-col items-center justify-center rounded-lg bg-gray-900/50 backdrop-blur-xs">
											<p class="mb-2 text-center text-white/90">Zobacz co twoi znajomi myślą o tym anime</p>
											<button on:click={handleLoginPrompt} class="cursor-pointer rounded bg-[#ee8585] px-4 py-2 text-sm font-semibold text-black transition-all hover:bg-[#8ec3f4]"> Zaloguj się </button>
										</div>
									{:else if provider === 'mal'}
										<div class="absolute inset-0 z-10 flex flex-col items-center justify-center rounded-lg bg-gray-900/50 backdrop-blur-xs">
											<p class="mb-2 text-center text-white/90">Oceny znajomych są dostępne<br /> tylko dla kont AniList</p>
											<a href="https://myanimelist.net/anime/{anime.mal_id}/Cowboy_Bepop/stats#members" target="_blank" rel="noopener noreferrer" class="rounded bg-[#ee8585] px-4 py-2 text-sm font-semibold text-black transition-all hover:bg-[#8ec3f4]"> Zobacz statystyki na MyAnimeList </a>
										</div>
									{/if}

									<div class="mb-16 flex flex-col gap-4 {!isLoggedIn || provider === 'mal' ? 'opacity-20' : ''}">
										{#if isLoggedIn && provider === 'anilist' && friendRatings.length > 0}
											{#each friendRatings as friend}
												<div class="flex items-center p-2 transition-all bg-gray-800 rounded-md">
													<img src={friend.user.avatar.large} alt="" class="w-10 h-10 mr-4 bg-gray-700 rounded-full shrink-0" />
													<div class="min-w-0 grow">
														<Tooltip.Root>
															<Tooltip.Trigger asChild>
																<h3 class="text-sm font-semibold truncate opacity-90">
																	{friend.user.name}
																</h3>
															</Tooltip.Trigger>
															<Tooltip.Content>
																<p class="text-sm">{friend.user.name}</p>
															</Tooltip.Content>
														</Tooltip.Root>
														<p class="text-xs text-gray-400">
															{#if friend.status === 'COMPLETED'}
																{anime.totalEpisodes} / {anime.totalEpisodes}
															{:else if friend.progress > 0}
																{friend.progress} / {anime.totalEpisodes ? anime.totalEpisodes : '?'}
															{/if}
															{#if friend.status === 'CURRENT'}
																• Oglądane
															{/if}
															{#if friend.status === 'PLANNING'}
																• Planowane
															{/if}
															{#if friend.status === 'COMPLETED'}
																• Ukończone
															{/if}
															{#if friend.status === 'DROPPED'}
																• Porzucone
															{/if}
															{#if friend.status === 'PAUSED'}
																• Wstrzymane
															{/if}
															{#if friend.status === 'REPEATING'}
																• Powtarzane
															{/if}
														</p>
													</div>
													{#if friend.score > 0}
														<div class="flex items-center mr-2 shrink-0">
															<Star class="w-4 h-4 mr-1 text-yellow-400 opacity-80" aria-hidden="true" />
															<span class="mt-[2px] font-semibold opacity-90">
																{friend.score.toFixed(1)}
															</span>
														</div>
													{/if}
												</div>
											{/each}
										{:else if isLoggedIn && provider === 'anilist'}
											<div class="flex flex-col items-center justify-center p-4 bg-gray-800 rounded-md">
												<p class="text-sm text-center text-gray-400">Nikt z twoich znajomych jeszcze nie ocenił tego anime</p>
											</div>
										{:else}
											{#each exampleRatings as friend}
												<div class="flex items-center p-2 transition-all bg-gray-800 rounded-md">
													<img src={friend.avatar} alt="" class="w-10 h-10 mr-4 bg-gray-700 rounded-full shrink-0" />
													<div class="min-w-0 overflow-hidden grow">
														<Tooltip.Root>
															<Tooltip.Trigger asChild>
																<h3 class="text-sm font-semibold truncate opacity-90">
																	{friend.name}
																</h3>
															</Tooltip.Trigger>
															<Tooltip.Content>
																<p class="text-sm">{friend.name}</p>
															</Tooltip.Content>
														</Tooltip.Root>
													</div>
													<div class="flex items-center ml-2 shrink-0">
														<Star class="w-4 h-4 mr-1 text-yellow-400 opacity-80" aria-hidden="true" />
														<span class="font-semibold opacity-90">
															{friend.rating.toFixed(1)}
														</span>
													</div>
												</div>
											{/each}
										{/if}
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="no-select mt-8 hidden w-full lg:-mt-[27vh] lg:block lg:w-2/5 lg:pl-4">
						<h2 class="sr-only">Odcinki</h2>

						<div class="p-4 mb-6 border rounded-lg shadow-md border-gray-700/50 bg-gray-800/70">
							<div class="flex items-center gap-2">
								<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#ee8585]">
									<path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
								</svg>
								<p class="text-sm text-gray-200">
									Odcinki możesz pobrać na naszym <a href="https://discord.gg/lycoriscafe" data-umami-event="openDiscordDownload" target="_blank" rel="noopener noreferrer" class="font-medium text-[#ee8585] transition-colors hover:text-[#8ec3f4] hover:underline">Discordzie</a>
								</p>
							</div>
						</div>

						{#each Array(anime.totalEpisodes || 12) as _, i}
							{@const episodeNum = i + 1}
							{@const anilistProgress = anime.episodeProgress || 0}
							{@const localProgress = $progressStore[anime.id]?.[episodeNum]?.progress || 0}
							{@const episode = anime.episodes.find((ep) => ep.number === episodeNum) || {
								number: episodeNum,
								title: `Odcinek ${episodeNum}`,
								thumbnail: anime.poster,
								airDate: (() => {
									const existingEpisode = anime.episodes.find((ep) => ep.number === episodeNum);
									if (existingEpisode) {
										return new Date(existingEpisode.airDate);
									}

									if (anime.nextAiringEpisode?.episode === episodeNum) {
										return new Date(anime.nextAiringEpisode.airingAt);
									}

									// Guard against empty episodes array
									if (!anime.episodes.length && !anime.nextAiringEpisode) {
										return new Date(); // Default to current date or another fallback
									}

									const lastKnownEpisode = anime.nextAiringEpisode || (anime.episodes.length > 0 ? anime.episodes[anime.episodes.length - 1] : null);

									// If there's no last known episode, return a default date
									if (!lastKnownEpisode) {
										return new Date();
									}

									const lastKnownDate = new Date(anime.nextAiringEpisode?.airingAt || (anime.episodes.length > 0 ? anime.episodes[anime.episodes.length - 1].airDate : Date.now()));

									const episodesDiff = episodeNum - lastKnownEpisode.episode;
									return new Date(lastKnownDate.getTime() + episodesDiff * 7 * 24 * 60 * 60 * 1000);
								})(),
								progress: 0,
								watched: false
							}}
							{@const isInDatabase = anime.episodes.some((ep) => ep.number === episodeNum)}
							{@const now = new Date()}
							{@const airDate = new Date(episode.airDate)}
							{@const isComingSoon = airDate <= now && !isInDatabase}
							{@const isReleased = airDate <= now && isInDatabase}
							{@const isWatched = episodeNum <= anilistProgress}
							{@const progress = isWatched ? 100 : episodeNum === anilistProgress + 1 ? localProgress : 0}
							{@const opacity = isComingSoon ? '0.4' : isReleased ? (isWatched || progress > 85 ? '0.65' : '0.9') : '0.4'}
							<a href={isReleased ? `${normalizeTitle(anime)}/watch/${episode.number}` : null} class="group relative mb-4 block h-24 rounded bg-black shadow-md transition-all duration-200 {isReleased ? 'cursor-pointer hover:opacity-100 md:hover:scale-105' : 'cursor-default'}" style="opacity: {opacity}; pointer-events: {isReleased ? 'auto' : 'none'};">
								<div class="absolute top-0 left-0 h-full overflow-hidden rounded-l w-36">
									<img src={episode.thumbnail} alt="" class="object-cover w-full h-full opacity-80" />
								</div>
								<div class="pt-2 pr-4 ml-40">
									<h3 class="text-sm font-semibold transition-all duration-200 {getWatchedClass(episode.number)} {isReleased ? 'group-hover:text-base group-hover:font-bold' : ''}">
										{#if episode.title?.includes(' - ')}
											{episode.number}. {episode.title.split(' - ')[1]}
										{:else if episode.title?.toLowerCase().startsWith('odcinek')}
											{episode.number}. {episode.title}
										{:else}
											{episode.number}. {episode.title || `Odcinek ${episode.number}`}
										{/if}
									</h3>
									<div class="w-full h-1 mt-2 bg-gray-700 rounded-full">
										<div class="h-full transition-all duration-200 rounded-full opacity-80" style="width: {getPercentageCompletion(episode.number)}%; background-color: {dominantColor};" aria-hidden="true"></div>
									</div>
									<span class="absolute text-xs text-gray-400 bottom-2 left-40">
										{isComingSoon ? 'Wkrótce!' : getEpisodeTimeStatus(episode.airDate)}
									</span>
								</div>
							</a>
						{/each}
					</div>
				</div>
			</div>
		</div>
	{:else}
		<div class="flex items-center justify-center h-screen" aria-label="Ładowanie szczegółów anime">
			<!-- Add a loading indicator here if needed -->
		</div>
	{/if}
	{#if anime}
		{#if isLoggedIn}
			<AnimeEditDialog bind:open={dialogOpen} {anime} onClose={closeDialog} onSave={saveChanges} onDelete={deleteEntry} />
		{:else}
			<AnimeEditLoginPrompt bind:open={dialogOpen} onClose={closeDialog} />
		{/if}
	{/if}
	<AnimeNavbar on:showStremioModal={() => (showStremioModal = true)} />
	<NavbarMobile />
	<UserProfileModal bind:open={showUserModal} user={null} onClose={handleCloseModal} />
</main>

{#if showStremioModal}
	<MoreModal {handleCloseStremioModal} />
{/if}

<style>
	/* Mobile landscape styles */
	@media (orientation: landscape) and (max-width: 1023px) {
		.mobile-landscape-wrapper {
			position: absolute;
			left: 190px;
			top: 7rem;
			width: calc(100% - 190px);
		}
	}

	/* Desktop styles */
	@media (min-width: 1024px) {
		.mobile-landscape-wrapper {
			position: static;
			left: auto;
			top: auto;
			width: auto;
		}
	}

	main {
		scroll-behavior: smooth;
	}

	.no-select {
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		user-select: none;
	}

	.line-clamp-3 {
		display: -webkit-box;
		line-clamp: 3;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.custom-scrollbar {
		scrollbar-width: thin;
		scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
	}

	.custom-scrollbar::-webkit-scrollbar {
		width: 6px;
		height: 6px;
	}

	.custom-scrollbar::-webkit-scrollbar-track {
		background: transparent;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb {
		background-color: rgba(156, 163, 175, 0.3);
		border-radius: 3px;
		border: none;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb:hover {
		background-color: rgba(156, 163, 175, 0.5);
	}

	/* Rest of existing styles */
	.line-clamp-3 {
		display: -webkit-box;
		line-clamp: 3;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
