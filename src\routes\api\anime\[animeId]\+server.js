import { json } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function GET({ params }) {
  try {
    const [episodesResponse, metadataResponse, rankingsResponse, relatedResponse] = await Promise.all([
      supabase
        .from('anime')
        .select('*')
        .eq('anilist_id', params.animeId),
      supabase
        .from('anime_metadata')
        .select('*')
        .eq('anilist_id', params.animeId)
        .single(),
      supabase
        .from('anime_rankings')
        .select('*')
        .eq('anilist_id', params.animeId)
        .single(),
      supabase
        .from('anime_relations')
        .select(`
          relation_type,
          target:target_id(
            anilist_id,
            romaji_title,
            cover_image,
            format
          )
        `)
        .eq('source_id', params.animeId)
    ]);

    if (episodesResponse.error) throw episodesResponse.error;
    if (metadataResponse.error) throw metadataResponse.error;
    if (rankingsResponse.error) throw rankingsResponse.error;
    if (relatedResponse.error) throw relatedResponse.error;

    if (!metadataResponse.data) {
      throw error(404, 'Anime not found');
    }

    if (metadataResponse.data && metadataResponse.data.hidden) {
      throw error(404, 'Anime not found');
    }

    const episodes = episodesResponse.data;
    const meta = metadataResponse.data;
    const ranking = rankingsResponse.data;

    const relatedAnime = relatedResponse.data
      ?.filter(relation => relation.target)
      ?.map(relation => ({
        id: relation.target.anilist_id,
        title: relation.target.romaji_title,
        poster: relation.target.cover_image,
        relation: relation.relation_type,
        type: relation.target.format
      })) || [];

    const now = new Date();
    const releasedEpisodes = episodes.filter(ep =>
      new Date(ep.date_added) <= now
    ).length;

    const getRanking = (type, format = null, timespan = 'all') => {
      const rankings = ranking?.rankings || [];
      let filtered = rankings.filter(r => r.type === type);

      if (format) {
        filtered = filtered.filter(r => r.format === format);
      }

      if (timespan === 'all') {
        filtered = filtered.filter(r => r.allTime);
      } else if (timespan === 'current') {
        filtered = filtered.filter(r => {
          const currentYear = new Date().getFullYear();
          return !r.allTime && r.year === currentYear;
        });
      }

      if (filtered.length === 0) return null;

      return {
        id: filtered[0].id,
        rank: filtered[0].rank,
        type: filtered[0].type,
        format: filtered[0].format,
        year: filtered[0].year,
        season: filtered[0].season,
        allTime: filtered[0].allTime,
        context: filtered[0].context
      };
    };

    const sortedEpisodes = episodes.sort((a, b) => a.episode_number - b.episode_number);
    const anime = {
      id: params.animeId,
      mal_id: meta.mal_id,
      title: meta.romaji_title,
      englishTitle: meta.english_title,
      nativeTitle: meta.native_title,
      poster: meta.cover_image,
      background: meta.banner_image,
      synopsis: meta.description,
      shortSynopsis: meta.description?.slice(0, 250) + (meta.description?.length >= 250 ? '...' : ''),
      rating: ranking?.average_score / 10,
      popularity: ranking?.popularity,
      trending: ranking?.trending,
      favourites: ranking?.favourites,
      rankings: {
        overall: getRanking('RATED', null, 'all'),
        current: getRanking('RATED', null, 'current'),
        popular: {
          all: getRanking('POPULAR', null, 'all'),
          current: getRanking('POPULAR', null, 'current'),
        },
        rated: {
          all: getRanking('RATED', null, 'all'),
          current: getRanking('RATED', null, 'current'),
        },
        format: {
          all: getRanking('RATED', meta.format, 'all'),
          current: getRanking('RATED', meta.format, 'current'),
        },
        raw: ranking?.rankings || []
      },
      format: meta.format,
      studio: meta.studios?.[0],
      source: meta.source,
      season: meta.season,
      seasonYear: meta.season_year,
      genres: meta.genres,
      totalEpisodes: meta.episodes,
      duration: meta.duration,
      delayedAiring: meta.delayed_airing,
      episodes: sortedEpisodes.map(ep => ({
        id: ep.id,
        mal_id: ep.mal_id,
        markerPeriods: ep.markerPeriods,
        anime_title: ep.anime_title,
        anilist_id: ep.anilist_id,
        number: ep.episode_number,
        title: ep.episode_title || `Episode ${ep.episode_number}`,
        thumbnail: ep.thumbnail_link,
        preview: ep.preview_file,
        burstSource: ep.burst_source,
        primarySource: ep.primary_source,
        secondarySource: ep.secondary_source,
        subtitles: ep.subtitleLinks,
        progress: 0,
        watched: false,
        airDate: ep.date_added,
        other_groups: ep.other_groups
      })),
      releasedEpisodes,
      airingStatus: ranking?.status,
      nextAiringEpisode: ranking?.airing_schedule ? {
        id: ranking.airing_schedule.id,
        episode: ranking.airing_schedule.episode,
        airingAt: (ranking.airing_schedule.airingAt * 1000) +
          (ranking.airing_schedule_offset || 0) * 60 * 60 * 1000
      } : null,
      startDate: meta.start_date,
      endDate: meta.end_date,
      lastUpdated: meta.last_updated,
      relatedEntries: relatedAnime
    };

    return json({ anime });

  } catch (e) {
    console.error('Error fetching anime:', e);

    // If it's already a 404 error thrown by our code, pass it through
    if (e.status === 404) {
      return new Response(JSON.stringify({ error: e.body?.message || 'Anime not found' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Handle database not found errors (PGRST116)
    if (e.code === 'PGRST116' || e.message?.includes('not found')) {
      return new Response(JSON.stringify({ error: 'Anime not found' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // For all other errors, return 500
    return new Response(JSON.stringify({ error: 'Failed to load anime data' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}