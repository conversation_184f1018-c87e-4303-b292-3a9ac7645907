<script>
	import { onMount } from 'svelte';
	import { writable, derived } from 'svelte/store';
	import InfiniteLoading from 'svelte-infinite-loading';
	import CommentBlock from '$lib/components/sections/comments/CommentBlock.svelte';
	import { Loader2 } from 'lucide-svelte';
	import { modalStore } from '$lib/stores/modal';

	export let comments = [];
	export let pageSize = 5;
	export let context = 'home';
	export let enforceMaxDepth = true;
	export let animeId = null;
	export let episodeNumber = null;
	export let preferRomaji;

	export let smHeight = '200px';
	export let mdHeight = '200px';
	export let lgHeight = '300px';
	export let xlHeight = '300px';

	const commentsStore = writable([]);
	let currentPage = 0;
	let infiniteKey = 0;
	let isLoading = writable(true);
	let isLoadingMore = writable(false);
	let hasMore = true;

	function buildCommentTree(comments) {
		const commentMap = new Map();
		const rootComments = [];

		// First pass: Create comment objects with replies arrays
		comments.forEach((comment) => {
			commentMap.set(comment.id, {
				...comment,
				replies: []
			});
		});

		// Second pass: Build the tree structure
		comments.forEach((comment) => {
			const commentWithReplies = commentMap.get(comment.id);
			if (comment.parent_id && commentMap.has(comment.parent_id)) {
				const parent = commentMap.get(comment.parent_id);
				parent.replies.push(commentWithReplies);
			} else if (!comment.parent_id) {
				rootComments.push(commentWithReplies);
			}
		});

		return rootComments.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
	}

	async function fetchMoreComments() {
		try {
			// Fetch root comments
			const response = await fetch(`/api/comments?page=${currentPage}&pageSize=${pageSize}${animeId ? `&animeId=${animeId}` : ''}${episodeNumber ? `&episodeNumber=${episodeNumber}` : ''}`);

			if (!response.ok) throw new Error('Failed to fetch comments');
			const { comments: rootComments } = await response.json();

			if (!rootComments.length) return [];

			// Fetch all replies
			const repliesResponse = await fetch(`/api/comments/replies?parentIds=${rootComments.map((c) => c.id).join(',')}`);

			if (!repliesResponse.ok) throw new Error('Failed to fetch replies');
			const { replies } = await repliesResponse.json();

			return [...rootComments, ...replies];
		} catch (error) {
			return [];
		}
	}

	$: visibleComments = derived(commentsStore, ($comments) => {
		return buildCommentTree($comments);
	});

	$: isLoadingAny = derived([isLoading, isLoadingMore], ([$isLoading, $isLoadingMore]) => $isLoading || $isLoadingMore);

	async function loadMoreComments({ detail: { loaded, complete } }) {
		isLoadingMore.set(true);
		try {
			const newComments = await fetchMoreComments();

			if (newComments.length > 0) {
				commentsStore.update((existingComments) => {
					const existingIds = new Set(existingComments.map((c) => c.id));
					const uniqueNewComments = newComments.filter((c) => !existingIds.has(c.id));
					return [...existingComments, ...uniqueNewComments];
				});

				currentPage++;
				hasMore = newComments.length >= pageSize;
				loaded();
			} else {
				hasMore = false;
				complete();
			}
		} catch (error) {
			complete();
		} finally {
			isLoadingMore.set(false);
		}
	}

	function resetInfiniteLoading() {
		currentPage = 0;
		hasMore = true;
		isLoading.set(true);

		fetchMoreComments().then((initialComments) => {
			commentsStore.set(initialComments);
			infiniteKey++;
			hasMore = initialComments.length >= pageSize;
			isLoading.set(false);
		});
	}

	onMount(() => {
		resetInfiniteLoading();
		return () => {};
	});

	$: {
		if (comments.length > 0) {
			commentsStore.set(comments);
		}
	}

	$: hasComments = derived(visibleComments, ($visibleComments) => $visibleComments.length > 0);
	$: commentCount = derived(commentsStore, ($comments) => $comments.length);
</script>

<div
	class="card-container recent-comments relative overflow-y-auto pr-2 pl-2 transition-all duration-300 ease-in-out"
	style="
    --sm-height: {context === 'episode' || (context === 'anime' && $commentCount < 3) ? 'auto' : smHeight}; 
    --md-height: {context === 'episode' || context === 'anime' ? 'auto' : mdHeight}; 
    --lg-height: {context === 'episode' ? 'auto' : lgHeight}; 
    --xl-height: {context === 'episode' ? 'auto' : xlHeight}; 
    min-height: {context === 'episode' || (context === 'anime' && $commentCount < 3) ? 'none' : 'none'};
    max-height: {context === 'episode' || context === 'anime' ? '600px' : 'none'};
  "
>
	{#if context === 'home'}
		<div class="sticky top-[-1px] z-10 rounded-lg bg-gray-800/30 text-center text-gray-400 backdrop-blur-xs">Ostatnie komentarze ze wszystkich anime</div>
	{/if}

	{#if $isLoading}
		<div class="flex items-center justify-center p-4">
			<Loader2 class="h-6 w-6 animate-spin text-blue-500" />
			<span class="ml-2 text-sm text-gray-400">Ładowanie komentarzy...</span>
		</div>
	{:else if !$hasComments}
		<div class="flex flex-col items-center justify-center p-4">
			<p class="text-gray-400">
				{#if context === 'episode'}
					Brak komentarzy dla tego odcinka. Bądź pierwszym, który skomentuje!
				{:else if context === 'anime'}
					<center>Brak komentarzy dla tego anime. <br /> Zostaw komentarz w dowolnym odcinku!</center>
				{:else}
					Brak dostępnych komentarzy.
				{/if}
			</p>
		</div>
	{:else}
		<div class="flex flex-col gap-4">
			{#each $visibleComments as comment, index (`${comment.id}-${comment.created_at}-${index}`)}
				<CommentBlock
					{preferRomaji}
					{comment}
					{context}
					maxDepth={4}
					{enforceMaxDepth}
					hideSubtitle={false}
					parentThreadLines={[]}
					onContinueThread={(comment) => {
						modalStore.open('fullscreen', {
							comment,
							context,
							maxDepth: 4,
							enforceMaxDepth
						});
					}}
				/>
			{/each}
		</div>

		{#if hasMore}
			<div class="mt-4">
				<InfiniteLoading on:infinite={loadMoreComments} key={infiniteKey} identifier={infiniteKey} distance={50} />
			</div>
		{/if}
	{/if}
</div>

<style>
	.card-container {
		height: var(--sm-height);
		scrollbar-width: thin;
		scrollbar-color: rgba(156, 163, 175, 0.5) rgba(31, 41, 55, 0.5);
	}

	.card-container::-webkit-scrollbar {
		width: 8px;
	}

	.card-container::-webkit-scrollbar-track {
		background: rgba(31, 41, 55, 0.5);
		border-radius: 4px;
	}

	.card-container::-webkit-scrollbar-thumb {
		background-color: rgba(156, 163, 175, 0.5);
		border-radius: 4px;
		border: 2px solid rgba(31, 41, 55, 0.5);
	}

	:global(.infinite-status-prompt) {
		display: none !important;
	}

	@media (max-width: 767px) {
		.card-container {
			height: var(--sm-height);
		}
	}

	@media (min-width: 768px) {
		.card-container {
			height: var(--md-height);
			min-height: 200px;
		}
	}

	@media (min-width: 1024px) {
		.card-container {
			height: var(--lg-height);
		}
	}

	@media (min-width: 1536px) {
		.card-container {
			height: var(--xl-height);
		}
	}
</style>
