<script>
	import { onMount } from 'svelte';
	import { RefreshCw, Filter, SortAsc, SortDesc } from 'lucide-svelte';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import DataTableFacetedFilter from './data-table-faceted-filter.svelte';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';

	export let data;
	export let onFilterChange;
	export let currentFilters;
	export let filterCounts;
	export let isLoading = false;

	let isMobileFilterOpen = false;
	let isMobileSortOpen = false; // New state for sort dialog
	let searchTimeout;
	let sortText = 'Sortowanie';

	onMount(() => {
		sortText = getCurrentSortLabel();
	});

	const genreTranslations = {
		Action: 'Akcja',
		Adventure: 'Przygodowe',
		Comedy: 'Komedia',
		Drama: 'Dramat',
		Fantasy: 'Fantasy',
		Horror: 'Horror',
		Mystery: 'Mystery',
		Romance: 'Romans',
		'Sci-Fi': 'Sci-Fi',
		'Slice of Life': 'Slice of Life',
		Sports: 'Sport',
		Supernatural: 'Nadprzyrodzone',
		Thriller: 'Thriller',
		Mecha: 'Mecha',
		Psychological: 'Psychologiczne',
		Music: 'Muzyczne',
		School: 'Szkolne',
		Seinen: 'Seinen',
		Shounen: 'Shounen',
		Shoujo: 'Shoujo',
		'Martial Arts': 'Sztuki Walki',
		Historical: 'Historyczne',
		Military: 'Militarne',
		Demons: 'Demony',
		Magic: 'Magia',
		Harem: 'Harem',
		Ecchi: 'Ecchi',
		Isekai: 'Isekai',
		Game: 'Gry',
		Parody: 'Parodia',
		Police: 'Policyjne',
		Space: 'Kosmos',
		Vampire: 'Wampiry'
	};

	const sourceTranslations = {
		MANGA: 'Manga',
		LIGHT_NOVEL: 'Light Novel',
		ORIGINAL: 'Oryginalne',
		VIDEO_GAME: 'Gra',
		VISUAL_NOVEL: 'Visual Novel',
		OTHER: 'Inne'
	};

	const seasonTranslations = {
		WINTER: 'Zima',
		SPRING: 'Wiosna',
		SUMMER: 'Lato',
		FALL: 'Jesień'
	};

	const formatTranslations = {
		MOVIE: 'Film',
		TV: 'TV',
		TV_SHORT: 'TV Short',
		ONA: 'ONA',
		SPECIAL: 'Specjalne'
	};

	const statusTranslations = {
		RELEASING: 'Emitowane',
		FINISHED: 'Zakończone'
	};

	const sortOptions = [
		{ value: 'popularity', label: 'Popularność', icon: SortDesc },
		{ value: 'title', label: 'Alfabetycznie', icon: SortAsc }
	];

	function translate(value, translationMap) {
		return translationMap[value] || value;
	}

	function getFilterOptions(filterType) {
		switch (filterType) {
			case 'genres':
				const allGenres = [
					'Action',
					'Adventure',
					'Comedy',
					'Drama',
					'Fantasy',
					'Mystery',
					'Romance',
					'Sci-Fi',
					'Slice of Life',
					'Sports',
					'Supernatural',
					'Thriller',
					'Mecha',
					'Psychological',
					'Ecchi'
				];
				return allGenres.map((genre) => ({
					value: genre,
					label: translate(genre, genreTranslations)
				}));
			case 'status':
				const allStatuses = ['RELEASING', 'FINISHED'];
				return allStatuses.map((status) => ({
					value: status,
					label: translate(status, statusTranslations)
				}));
			case 'format':
				const allFormats = ['TV', 'MOVIE', 'SPECIAL', 'ONA', 'TV_SHORT'];
				return allFormats.map((format) => ({
					value: format,
					label: translate(format, formatTranslations)
				}));
			case 'year':
				return [...new Set(data.map((anime) => anime.seasonYear).filter(Boolean))]
					.sort((a, b) => b - a)
					.map((year) => ({
						value: year.toString(),
						label: year.toString()
					}));
			case 'season':
				return [...new Set(data.map((anime) => anime.season).filter(Boolean))].map((season) => ({
					value: season,
					label: translate(season, seasonTranslations)
				}));
			case 'source':
				const allSources = ['MANGA', 'LIGHT_NOVEL', 'VISUAL_NOVEL', 'ORIGINAL', 'OTHER', 'VIDEO_GAME'];
				return allSources.map((source) => ({
					value: source,
					label: translate(source, sourceTranslations)
				}));
			default:
				return [];
		}
	}

	$: showReset = currentFilters.search || Object.values(currentFilters).some((v) => (Array.isArray(v) ? v.length > 0 : v));

	function handleSearch(e) {
		clearTimeout(searchTimeout);
		const searchValue = e.target.value;

		searchTimeout = setTimeout(() => {
			handleFilterChange('search', searchValue);
		}, 500);
	}

	async function handleFilterChange(filterType, values) {
		const updatedFilters = {
			...currentFilters,
			[filterType]: values
		};
		await onFilterChange(updatedFilters);
	}

	async function handleSortChange(field, direction) {
		// Create the updated filters object
		const updatedFilters = {
			...currentFilters,
			sortField: field,
			sortDirection: direction
		};

		// Update parent component
		await onFilterChange(updatedFilters);

		// Update local reference to currentFilters
		currentFilters = updatedFilters;
		sortText = getCurrentSortLabel();
		// Close mobile sort dialog if open
		if (isMobileSortOpen) {
			isMobileSortOpen = false;
		}
	}

	async function resetFilters() {
		const emptyFilters = {
			search: '',
			genres: [],
			status: [],
			format: [],
			year: [],
			season: [],
			source: [],
			// Maintain the current sort settings
			sortField: currentFilters.sortField || 'popularity',
			sortDirection: currentFilters.sortDirection || 'desc'
		};
		await onFilterChange(emptyFilters);
	}

	function toggleMobileFilter() {
		isMobileFilterOpen = !isMobileFilterOpen;
	}

	function toggleMobileSort() {
		isMobileSortOpen = !isMobileSortOpen;
	}

	function getCurrentSortLabel() {
		if (currentFilters.sortField === 'popularity' && currentFilters.sortDirection === 'desc') {
			return 'Najpopularniejsze';
		} else if (currentFilters.sortField === 'popularity' && currentFilters.sortDirection === 'asc') {
			return 'Najmniej popularne';
		} else if (currentFilters.sortField === 'title' && currentFilters.sortDirection === 'asc') {
			return 'Tytuł A-Z';
		} else if (currentFilters.sortField === 'title' && currentFilters.sortDirection === 'desc') {
			return 'Tytuł Z-A';
		} else if (currentFilters.sortField === 'rating' && currentFilters.sortDirection === 'desc') {
			return 'Najwyżej oceniane';
		} else if (currentFilters.sortField === 'rating' && currentFilters.sortDirection === 'asc') {
			return 'Najniżej oceniane';
		} else {
			return 'Sortowanie';
		}
	}

	function getSortIcon() {
		const sortField = currentFilters.sortField || 'popularity';
		const sortDirection = currentFilters.sortDirection || 'desc';
		return sortDirection === 'desc' ? SortDesc : SortAsc;
	}
</script>

<div class="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
	<div class="flex flex-1 items-center space-x-2">
		<Input placeholder="Szukaj anime..." class="h-8 w-full md:w-[150px] lg:w-[250px]" type="search" value={currentFilters.search} on:input={handleSearch} {isLoading} />

		<!-- Sort button for mobile - icon only -->
		<Button on:click={toggleMobileSort} variant="outline" class="h-8 cursor-pointer md:hidden" {isLoading}>
			<svelte:component this={getSortIcon()} class="h-4 w-4" />
		</Button>

		<!-- Sort Dropdown for desktop -->
		<div class="hidden md:block">
			<DropdownMenu.Root class="hidden md:block">
				<DropdownMenu.Trigger asChild let:builder>
					<Button variant="outline" builders={[builder]} class="h-8 cursor-pointer" {isLoading}>
						<svelte:component this={getSortIcon()} class="mr-2 h-4 w-4" />
						{sortText}
					</Button>
				</DropdownMenu.Trigger>
				<DropdownMenu.Content align="start">
					<DropdownMenu.Group>
						<DropdownMenu.Label>Sortuj według</DropdownMenu.Label>
						<DropdownMenu.Separator />
						<!-- Popularity sort options -->
						<DropdownMenu.Item on:click={() => handleSortChange('popularity', 'desc')} class="cursor-pointer">Najpopularniejsze</DropdownMenu.Item>
						<DropdownMenu.Item on:click={() => handleSortChange('popularity', 'asc')} class="cursor-pointer">Najmniej popularne</DropdownMenu.Item>
						<!-- Alphabetical sort options -->
						<DropdownMenu.Item on:click={() => handleSortChange('title', 'asc')} class="cursor-pointer">Tytuł A-Z</DropdownMenu.Item>
						<DropdownMenu.Item on:click={() => handleSortChange('title', 'desc')} class="cursor-pointer">Tytuł Z-A</DropdownMenu.Item>
						<!-- Rating sort options -->
						<DropdownMenu.Item on:click={() => handleSortChange('rating', 'desc')} class="cursor-pointer">Najwyżej oceniane</DropdownMenu.Item>
						<DropdownMenu.Item on:click={() => handleSortChange('rating', 'asc')} class="cursor-pointer">Najniżej oceniane</DropdownMenu.Item>
					</DropdownMenu.Group>
				</DropdownMenu.Content>
			</DropdownMenu.Root>
		</div>

		<!-- Mobile filter button -->
		<Button on:click={toggleMobileFilter} variant="outline" class="md:hidden" {isLoading}>
			<Filter class="mr-2 h-4 w-4" />
			<span class="hidden md:block">Filtry</span>
		</Button>

		<!-- Desktop filters -->
		<div class="hidden md:flex md:space-x-2">
			{#each ['genres', 'status', 'format', 'year', 'season', 'source'] as filterType}
				<DataTableFacetedFilter
					filterValues={currentFilters[filterType]}
					title={filterType === 'genres'
						? 'Gatunki'
						: filterType === 'status'
							? 'Status'
							: filterType === 'format'
								? 'Format'
								: filterType === 'year'
									? 'Rok'
									: filterType === 'season'
										? 'Sezon'
										: filterType === 'source'
											? 'Źródło'
											: filterType}
					options={getFilterOptions(filterType)}
					counts={filterCounts?.[filterType] || {}}
					onFilterChange={(values) => handleFilterChange(filterType, values)}
					{isLoading}
				/>
			{/each}
		</div>

		{#if showReset}
			<Button on:click={resetFilters} variant="ghost" class="hidden h-8 cursor-pointer px-2 md:flex lg:px-3" {isLoading}>
				Resetuj
				<RefreshCw class="ml-2 h-4 w-4" />
			</Button>
		{/if}
	</div>
</div>

<!-- Mobile filter dialog -->
<Dialog.Root bind:open={isMobileFilterOpen}>
	<Dialog.Content class="sm:max-w-[425px]">
		<Dialog.Header>
			<Dialog.Title>Filtry</Dialog.Title>
			<Dialog.Description>Zastosuj filtry, aby zawęzić wybór anime.</Dialog.Description>
		</Dialog.Header>
		<div class="grid gap-4 py-4">
			{#each ['genres', 'status', 'format', 'year', 'season', 'source'] as filterType}
				<DataTableFacetedFilter
					filterValues={currentFilters[filterType]}
					title={filterType === 'genres'
						? 'Gatunki'
						: filterType === 'status'
							? 'Status'
							: filterType === 'format'
								? 'Format'
								: filterType === 'year'
									? 'Rok'
									: filterType === 'season'
										? 'Sezon'
										: filterType === 'source'
											? 'Źródło'
											: filterType}
					options={getFilterOptions(filterType)}
					counts={filterCounts?.[filterType] || {}}
					onFilterChange={(values) => handleFilterChange(filterType, values)}
					{isLoading}
				/>
			{/each}
		</div>
		<Dialog.Footer>
			<Button on:click={toggleMobileFilter} {isLoading}>Zamknij</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>

<!-- Mobile sort dialog (NEW) -->
<Dialog.Root bind:open={isMobileSortOpen}>
	<Dialog.Content class="sm:max-w-[425px]">
		<Dialog.Header>
			<Dialog.Title>Sortowanie</Dialog.Title>
			<Dialog.Description>Wybierz sposób sortowania wyników.</Dialog.Description>
		</Dialog.Header>
		<div class="grid gap-4 py-4">
			<div class="space-y-2">
				<div class="grid grid-cols-1 gap-2">
					<!-- Popularity sort options -->
					<Button
						variant={currentFilters.sortField === 'popularity' && currentFilters.sortDirection === 'desc' ? 'default' : 'outline'}
						size="sm"
						on:click={() => handleSortChange('popularity', 'desc')}
					>
						Najpopularniejsze
					</Button>
					<Button
						variant={currentFilters.sortField === 'popularity' && currentFilters.sortDirection === 'asc' ? 'default' : 'outline'}
						size="sm"
						on:click={() => handleSortChange('popularity', 'asc')}
					>
						Najmniej popularne
					</Button>

					<!-- Alphabetical sort options -->
					<Button variant={currentFilters.sortField === 'title' && currentFilters.sortDirection === 'asc' ? 'default' : 'outline'} size="sm" on:click={() => handleSortChange('title', 'asc')}>
						Tytuł A-Z
					</Button>
					<Button variant={currentFilters.sortField === 'title' && currentFilters.sortDirection === 'desc' ? 'default' : 'outline'} size="sm" on:click={() => handleSortChange('title', 'desc')}>
						Tytuł Z-A
					</Button>

					<!-- Rating sort options -->
					<Button variant={currentFilters.sortField === 'rating' && currentFilters.sortDirection === 'desc' ? 'default' : 'outline'} size="sm" on:click={() => handleSortChange('rating', 'desc')}>
						Najwyżej oceniane
					</Button>
					<Button variant={currentFilters.sortField === 'rating' && currentFilters.sortDirection === 'asc' ? 'default' : 'outline'} size="sm" on:click={() => handleSortChange('rating', 'asc')}>
						Najniżej oceniane
					</Button>
				</div>
			</div>
		</div>
		<Dialog.Footer>
			<Button on:click={toggleMobileSort} {isLoading}>Zamknij</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>

<style>
	:global(.filter-dialog-content) {
		max-height: 80vh;
		overflow-y: auto;
	}
</style>
