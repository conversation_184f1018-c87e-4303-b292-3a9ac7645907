<script>
	import { fade } from 'svelte/transition';
	import { ChevronLeft, ChevronRight } from 'lucide-svelte';
	import { Button } from '$lib/components/ui/button';
	import { format, addHours } from 'date-fns';
	import { onMount } from 'svelte';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { generateAnimeUrl } from '$lib/myUtils';
	import { pl } from 'date-fns/locale';
	import { getPreferredTitle } from '$lib/utils/titleHelper';

	// Props from parent
	export let data, userSettings;
	let preferRomaji;
	if (!userSettings?.titleLanguage) {
		preferRomaji = true;
	} else {
		preferRomaji = userSettings.titleLanguage === 'romaji';
	}

	let isReady = false;
	let currentDate = new Date();
	let selectedDate = new Date();
	let scheduleData = data.releaseScheduleData;
	let isLoading = false;

	// Apply offset to a date - only for non-aired episodes
	function applyOffset(date, offset, isAired) {
		if (!date) return null;

		// For aired episodes, use the actual date without offset
		if (isAired) {
			return new Date(date);
		}

		// For upcoming episodes, apply the offset
		const newDate = new Date(date);
		newDate.setHours(newDate.getHours() + parseFloat(offset || 0));
		return newDate;
	}

	// Determine if a show is delayed (>7 days from now)
	function isDelayed(show) {
		if (!show.airing_time || show.status === 'aired') return false;

		const airDate = applyOffset(new Date(show.airing_time), show.airing_schedule_offset, false);
		const now = new Date();
		const sevenDaysFromNow = new Date();
		sevenDaysFromNow.setDate(now.getDate() + 7);

		return airDate > sevenDaysFromNow;
	}

	async function fetchScheduleForDate(date) {
		isLoading = true;
		try {
			const response = await fetch('/api/releaseSchedule', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					date: date.toISOString()
				})
			});

			if (!response.ok) throw new Error('Failed to fetch schedule');

			const newData = await response.json();
			scheduleData = newData;
		} catch (error) {
			console.error('Error fetching schedule:', error);
		} finally {
			isLoading = false;
		}
	}

	// Calculate week dates
	$: weekDates = Array.from({ length: 7 }, (_, i) => {
		const date = new Date(currentDate);
		// Adjust the calculation to start from Monday (1) instead of Sunday (0)
		const currentDay = date.getDay();
		const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay;
		date.setDate(date.getDate() + mondayOffset + i);
		return date;
	});

	// Process schedule data with offset applied for date comparison
	$: processedScheduleData = scheduleData.map((show) => {
		const isAired = show.status === 'aired';
		const offsetAiringTime = applyOffset(new Date(show.airing_time), show.airing_schedule_offset, isAired);

		return {
			...show,
			offsetAiringTime,
			isDelayedStatus: isDelayed(show)
		};
	});

	// Filter schedule data for selected date with offset applied
	$: dailySchedule = processedScheduleData
		.filter((show) => {
			return show.offsetAiringTime.toDateString() === selectedDate.toDateString();
		})
		.sort((a, b) => {
			// Sort by the offset time
			return a.offsetAiringTime.getTime() - b.offsetAiringTime.getTime();
		});

	function isPastDate(date) {
		return new Date(date) < new Date();
	}

	async function previousWeek() {
		currentDate.setDate(currentDate.getDate() - 7);
		currentDate = new Date(currentDate);
	}

	async function nextWeek() {
		currentDate.setDate(currentDate.getDate() + 7);
		currentDate = new Date(currentDate);
	}

	async function selectDate(date) {
		selectedDate = date;
		const today = new Date();
		today.setHours(0, 0, 0, 0);

		// Only fetch if selected date is different from today
		if (selectedDate.getTime() !== today.getTime()) {
			await fetchScheduleForDate(selectedDate);
		} else {
			scheduleData = data.releaseScheduleData;
		}
	}

	function formatTime(date, offset, isAired) {
		const d = new Date(date);

		// For aired episodes, round the actual time
		if (isAired) {
			const minutes = d.getMinutes();

			// Round to nearest half hour
			if (minutes >= 45) {
				d.setHours(d.getHours() + 1);
				d.setMinutes(0);
			} else if (minutes >= 15 && minutes < 45) {
				d.setMinutes(30);
			} else {
				d.setMinutes(0);
			}

			d.setSeconds(0);
			return format(d, d.getMinutes() === 30 ? 'HH:30' : 'HH:00');
		} else {
			// For upcoming episodes, apply offset and add one hour
			const offsetDate = applyOffset(d, offset, false);
			const minutes = offsetDate.getMinutes();

			// Round to nearest half hour
			if (minutes >= 45) {
				offsetDate.setHours(offsetDate.getHours() + 1);
				offsetDate.setMinutes(0);
			} else if (minutes >= 15 && minutes < 45) {
				offsetDate.setMinutes(30);
			} else {
				offsetDate.setMinutes(0);
			}

			offsetDate.setSeconds(0);
			// Add one additional hour to the display time for upcoming episodes
			return format(addHours(offsetDate, 1), offsetDate.getMinutes() === 30 ? 'HH:30' : 'HH:00');
		}
	}

	function formatMobileDate(date) {
		return format(date, 'd/M');
	}

	function getShowStatus(show) {
		if (show.status === 'aired') return 'aired';
		if (show.status === 'coming-soon') return 'coming-soon';
		return 'upcoming';
	}

	onMount(() => {
		isReady = true;
	});
</script>

<section class="relative z-10 mt-[5rem] mb-10">
	<div class="flex items-center justify-between px-4 mb-4">
		<h2 class="text-xl font-bold text-white opacity-80 md:text-3xl">Planowane Premiery</h2>
		<div class="flex items-center">
			<span class="mt-[1px] mr-0 text-sm text-gray-400 md:mt-0 md:mr-0">
				{format(new Date(), 'dd.MM.yyyy HH:mm:ss', { locale: pl })}
			</span>
		</div>
	</div>

	<div class="relative px-6">
		<div class="flex items-center justify-between mb-4">
			<Button variant="ghost" size="icon" on:click={previousWeek} class="text-gray-400 rounded-full cursor-pointer hover:text-white">
				<ChevronLeft class="w-6 h-6" />
			</Button>

			<div class="flex space-x-1 md:space-x-4">
				{#each weekDates as date}
					<button
						class="flex cursor-pointer flex-col items-center rounded-lg px-2 py-1.5 transition-all duration-300 md:px-4 md:py-2 {date.toDateString() === selectedDate.toDateString()
							? 'bg-blue-300 text-gray-900'
							: 'hover:bg-gray-800'}"
						on:click={() => selectDate(date)}
					>
						<span class="text-xs font-medium md:text-sm">
							{format(date, 'EEE', { locale: pl })}
						</span>
						<span class="text-[10px] opacity-80 md:text-xs">
							{formatMobileDate(date)}
						</span>
					</button>
				{/each}
			</div>

			<Button variant="ghost" size="icon" on:click={nextWeek} class="text-gray-400 rounded-full cursor-pointer hover:text-white">
				<ChevronRight class="w-6 h-6" />
			</Button>
		</div>

		{#if isLoading}
			<div class="space-y-4">
				{#each Array(3) as _}
					<div class="flex items-center p-4 rounded-lg bg-gray-800/50">
						<Skeleton class="w-12 h-12 mr-4 rounded-md" />
						<div class="flex-1">
							<Skeleton class="w-2/3 h-4 mb-2" />
							<Skeleton class="w-1/3 h-3" />
						</div>
					</div>
				{/each}
			</div>
		{:else}
			<div class="space-y-4">
				{#each dailySchedule as show}
					<a
						href={show.status === 'aired' ? `${generateAnimeUrl(show)}/watch/${show.episode}` : generateAnimeUrl(show)}
						class="flex items-center p-4 transition-all duration-300 rounded-lg bg-gray-800/50 hover:bg-gray-800"
					>
						<div class="w-12 h-12 mr-4">
							<img src={show.image} alt="" class="object-cover w-full h-full rounded-md" loading="lazy" />
						</div>
						<div class="flex-1">
							<h3 class="text-sm font-medium text-white">
								{getPreferredTitle(show, preferRomaji)}
							</h3>
							<p class="text-xs text-gray-400">
								Odcinek {show.episode} • {formatTime(show.airing_time, show.airing_schedule_offset, show.status === 'aired')}
								{#if show.status === 'aired'}
									<span class="ml-2 text-green-400">Wyemitowano</span>
								{:else if show.status === 'coming-soon'}
									<span class="ml-2 text-yellow-400">Wkrótce!</span>
								{:else}
									<span class="ml-2 text-blue-400">Nadchodzące</span>
								{/if}
							</p>
						</div>
					</a>
				{/each}

				{#if dailySchedule.length === 0}
					<div class="flex items-center justify-center p-8 text-gray-400">Brak zaplanowanych premier na ten dzień</div>
				{/if}
			</div>
		{/if}
	</div>
</section>
