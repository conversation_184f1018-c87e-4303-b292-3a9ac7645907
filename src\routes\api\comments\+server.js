import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY, REPORT_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function GET({ url, locals }) {
  const animeId = url.searchParams.get('animeId');
  const episodeNumber = url.searchParams.get('episodeNumber');
  const page = parseInt(url.searchParams.get('page')) || 0;
  const pageSize = parseInt(url.searchParams.get('pageSize')) || 5;
  const adminKey = url.searchParams.get('adminKey');

  let includeAllComments = false;
  let userId = null;

  // Get current user if logged in
  const { session, user } = await locals.safeGetSession();
  if (session && user) {
    userId = user.id;

    // Check for admin privileges if adminKey is provided
    if (adminKey === REPORT_KEY) {
      const { data: profile } = await supabase
        .from('profile')
        .select('role')
        .eq('id', user.id)
        .single();

      if (profile?.role === 'admin') {
        includeAllComments = true;
      }
    }
  }

  try {
    // Call the RPC function with user ID
    const { data: comments, error: queryError } = await supabase
      .rpc('get_comments', {
        p_anime_id: animeId ? parseInt(animeId) : null,
        p_episode_number: episodeNumber ? parseInt(episodeNumber) : null,
        p_page: page,
        p_page_size: pageSize,
        p_include_all_comments: includeAllComments,
        p_user_id: userId
      });

    if (queryError) throw queryError;

    // Get replies for the fetched comments
    const commentIds = comments.map(comment => comment.id);
    let replies = [];

    if (commentIds.length > 0) {
      let repliesBuilder = supabase
        .from('comments')
        .select('*')
        .in('parent_id', commentIds);

      if (!includeAllComments) {
        repliesBuilder = repliesBuilder
          .eq('is_pending_review', false)
          .eq('is_spam', false);
      }

      repliesBuilder = repliesBuilder.order('created_at', { ascending: true });

      const { data: repliesData, error: repliesError } = await repliesBuilder;

      if (repliesError) throw repliesError;

      // Get user reactions for replies if user is logged in
      if (userId && repliesData && repliesData.length > 0) {
        const replyIds = repliesData.map(reply => reply.id);

        const { data: userReactions, error: reactionsError } = await supabase
          .from('comment_reactions')
          .select('comment_id, reaction_type')
          .eq('user_id', userId)
          .in('comment_id', replyIds);

        if (reactionsError) throw reactionsError;

        // Create a map of comment_id to reactions
        const reactionsMap = {};
        if (userReactions) {
          userReactions.forEach(reaction => {
            if (!reactionsMap[reaction.comment_id]) {
              reactionsMap[reaction.comment_id] = [];
            }
            reactionsMap[reaction.comment_id].push(reaction.reaction_type);
          });
        }

        // Add user_reactions to each reply WITHOUT reassigning repliesData
        // Instead, create a new array with the reactions added
        replies = repliesData.map(reply => ({
          ...reply,
          user_reactions: reactionsMap[reply.id] || []
        }));
      } else {
        replies = repliesData || [];
      }
    }

    // Organize replies into a nested structure
    const commentsWithReplies = comments.map(comment => ({
      ...comment,
      anime_title: comment.anime_title,
      replies: replies.filter(reply => reply.parent_id === comment.id)
    }));

    return json({
      comments: commentsWithReplies,
      hasMore: comments.length === pageSize,
      page,
      pageSize,
      isAdmin: includeAllComments
    });

  } catch (error) {
    console.error('Error fetching comments:', error);
    return new Response(JSON.stringify({
      error: error.message,
      details: error.details || {},
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}