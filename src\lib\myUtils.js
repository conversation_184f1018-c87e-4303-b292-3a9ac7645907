const CACHE_VERSION = 1;
const CACHE_KEY_PREFIX = 'dominantColor_';

export function truncateText(text, maxLength) {
	if (text.length > maxLength) {
		return text.substring(0, maxLength) + '...';
	}
	return text;
}

// In-memory cache
const memoryCache = new Map();

export function getCachedColor(imageUrl) {
	// Check in-memory cache first
	if (memoryCache.has(imageUrl)) {
		return memoryCache.get(imageUrl);
	}

	// Check session storage
	const cacheKey = `${CACHE_KEY_PREFIX}${imageUrl}`;
	const cachedData = sessionStorage.getItem(cacheKey);

	if (cachedData) {
		const { color, version } = JSON.parse(cachedData);
		if (version === CACHE_VERSION) {
			// Store in memory cache for faster subsequent access
			memoryCache.set(imageUrl, color);
			return color;
		}
	}

	return null;
}

export function setCachedColor(imageUrl, color) {
	const cacheKey = `${CACHE_KEY_PREFIX}${imageUrl}`;
	const cacheData = JSON.stringify({ color, version: CACHE_VERSION });

	// Store in session storage
	sessionStorage.setItem(cacheKey, cacheData);

	// Store in memory cache
	memoryCache.set(imageUrl, color);
}

export function clearColorCache() {
	// Clear memory cache
	memoryCache.clear();

	// Clear session storage cache
	Object.keys(sessionStorage).forEach((key) => {
		if (key.startsWith(CACHE_KEY_PREFIX)) {
			sessionStorage.removeItem(key);
		}
	});
}

export function generateAnimeUrl(anime) {
	if (!anime.title) {
		return '/'
	}
	const normalizedTitle = anime.title
		.toLowerCase()
		.replace(/[^a-z0-9]+/g, '-')
		.replace(/^-+|-+$/g, '');
	return `/anime/${anime.id}/${normalizedTitle}`;
}

export function normalizeTitle(anime) {
	const normalizedTitle = anime.title
		.toLowerCase()
		.replace(/[^a-z0-9]+/g, '-')
		.replace(/^-+|-+$/g, '');
	return normalizedTitle;
}
