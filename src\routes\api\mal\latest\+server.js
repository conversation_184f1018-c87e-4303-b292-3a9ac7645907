// src/routes/api/mal/latest/+server.js
import { json } from '@sveltejs/kit';

export async function POST({ request, locals: { supabase } }) {
    const { animeIds } = await request.json();

    if (!animeIds || !Array.isArray(animeIds)) {
        return json({ latestEpisodes: [] });
    }

    const { data: latestEpisodes, error } = await supabase
        .rpc('get_latest_mal_episode_numbers', {
            anime_ids: animeIds
        });

    if (error) {
        console.error('Error fetching latest MAL episodes:', error);
        return json({ latestEpisodes: [] });
    }

    // Ensure all requested IDs have an entry, even if no episodes were found
    const results = animeIds.map(id => ({
        mal_id: id,
        latest_episode: latestEpisodes.find(ep => ep.mal_id === id)?.latest_episode || 0
    }));

    return json({ latestEpisodes: results });
}